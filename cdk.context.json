{"availability-zones:account=************:region=ap-southeast-2": ["ap-southeast-2a", "ap-southeast-2b", "ap-southeast-2c"], "vpc-provider:account=************:filter.tag:Name=martini-vpc:region=ap-southeast-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-0ce2c493482dd19be", "vpcCidrBlock": "**********/16", "availabilityZones": [], "subnetGroups": [{"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-02ad7bd47a590fbdf", "cidr": "************/21", "availabilityZone": "ap-southeast-2a", "routeTableId": "rtb-055c6cb73319d5f55"}, {"subnetId": "subnet-03dc74f6f72c6c32a", "cidr": "************/27", "availabilityZone": "ap-southeast-2a", "routeTableId": "rtb-05ace57a1dc948b2c"}, {"subnetId": "subnet-083125f10f7f9838b", "cidr": "*************/27", "availabilityZone": "ap-southeast-2b", "routeTableId": "rtb-05ace57a1dc948b2c"}, {"subnetId": "subnet-0ccd3636602fffcc7", "cidr": "************/21", "availabilityZone": "ap-southeast-2b", "routeTableId": "rtb-055c6cb73319d5f55"}]}, {"name": "Private", "type": "Private", "subnets": [{"subnetId": "subnet-07da3846f15ca3933", "cidr": "**********/21", "availabilityZone": "ap-southeast-2a", "routeTableId": "rtb-0637d1d9aae47ec58"}, {"subnetId": "subnet-0ba274cc6c0ae9075", "cidr": "10.200.8.0/21", "availabilityZone": "ap-southeast-2b", "routeTableId": "rtb-0276707d59d6d6a0b"}]}]}, "security-group:account=************:region=ap-southeast-2:securityGroupId=sg-041580341175fa956": {"securityGroupId": "sg-041580341175fa956", "allowAllOutbound": false}, "security-group:account=************:region=ap-southeast-2:securityGroupId=sg-0dd3cfa06720973f1": {"securityGroupId": "sg-0dd3cfa06720973f1", "allowAllOutbound": false}, "load-balancer:account=************:loadBalancerTags.0.key=Name:loadBalancerTags.0.value=martini-frontend-pub-alb:loadBalancerTags.1.key=Env:loadBalancerTags.1.value=MARTINI:loadBalancerTags.2.key=Region:loadBalancerTags.2.value=AP-SOUTHEAST-2:loadBalancerType=application:region=ap-southeast-2": {"loadBalancerArn": "arn:aws:elasticloadbalancing:ap-southeast-2:************:loadbalancer/app/martini-frontend-pub-alb/a06c5444d3898944", "loadBalancerCanonicalHostedZoneId": "Z1GM3OXH4ZPM65", "loadBalancerDnsName": "martini-frontend-pub-alb-**********.ap-southeast-2.elb.amazonaws.com", "vpcId": "vpc-0ce2c493482dd19be", "securityGroupIds": ["sg-0fe8c31e094c253fb"], "ipAddressType": "ipv4"}, "vpc-provider:account=************:filter.vpc-id=vpc-0ce2c493482dd19be:region=ap-southeast-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-0ce2c493482dd19be", "vpcCidrBlock": "**********/16", "availabilityZones": [], "subnetGroups": [{"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-02ad7bd47a590fbdf", "cidr": "************/21", "availabilityZone": "ap-southeast-2a", "routeTableId": "rtb-055c6cb73319d5f55"}, {"subnetId": "subnet-03dc74f6f72c6c32a", "cidr": "************/27", "availabilityZone": "ap-southeast-2a", "routeTableId": "rtb-05ace57a1dc948b2c"}, {"subnetId": "subnet-083125f10f7f9838b", "cidr": "*************/27", "availabilityZone": "ap-southeast-2b", "routeTableId": "rtb-05ace57a1dc948b2c"}, {"subnetId": "subnet-0ccd3636602fffcc7", "cidr": "************/21", "availabilityZone": "ap-southeast-2b", "routeTableId": "rtb-055c6cb73319d5f55"}]}, {"name": "Private", "type": "Private", "subnets": [{"subnetId": "subnet-07da3846f15ca3933", "cidr": "**********/21", "availabilityZone": "ap-southeast-2a", "routeTableId": "rtb-0637d1d9aae47ec58"}, {"subnetId": "subnet-0ba274cc6c0ae9075", "cidr": "10.200.8.0/21", "availabilityZone": "ap-southeast-2b", "routeTableId": "rtb-0276707d59d6d6a0b"}]}]}, "security-group:account=************:region=ap-southeast-2:securityGroupId=sg-0fe8c31e094c253fb": {"securityGroupId": "sg-0fe8c31e094c253fb", "allowAllOutbound": false}, "load-balancer-listener:account=************:listenerArn=arn$:aws:elasticloadbalancing:ap-southeast-2:************:listener/app/martini-frontend-pub-alb/a06c5444d3898944/fc3532a68d02ea92:loadBalancerType=application:region=ap-southeast-2": {"listenerArn": "arn:aws:elasticloadbalancing:ap-southeast-2:************:listener/app/martini-frontend-pub-alb/a06c5444d3898944/fc3532a68d02ea92", "listenerPort": 80, "securityGroupIds": ["sg-0fe8c31e094c253fb"]}, "load-balancer-listener:account=************:listenerArn=arn$:aws:elasticloadbalancing:ap-southeast-2:************:listener/app/martini-frontend-pub-alb/a06c5444d3898944/97e97c8b5e03a2c8:loadBalancerType=application:region=ap-southeast-2": {"listenerArn": "arn:aws:elasticloadbalancing:ap-southeast-2:************:listener/app/martini-frontend-pub-alb/a06c5444d3898944/97e97c8b5e03a2c8", "listenerPort": 443, "securityGroupIds": ["sg-0fe8c31e094c253fb"]}, "load-balancer-listener:account=************:listenerArn=arn$:aws$:elasticloadbalancing$:ap-southeast-2$:************$:listener/app/martini-frontend-pub-alb/a06c5444d3898944/97e97c8b5e03a2c8:loadBalancerType=application:region=ap-southeast-2": {"listenerArn": "arn:aws:elasticloadbalancing:ap-southeast-2:************:listener/app/martini-frontend-pub-alb/a06c5444d3898944/97e97c8b5e03a2c8", "listenerPort": 443, "securityGroupIds": ["sg-0fe8c31e094c253fb"]}}