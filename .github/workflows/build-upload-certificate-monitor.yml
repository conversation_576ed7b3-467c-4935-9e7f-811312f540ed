---
name: Build & Upload Certificate Monitor
on:
  push:
    paths:
      - '.github/workflows/build-lambda.yml'
      - '.github/workflows/build-upload-certificate-monitor.yml'
      - 'packages/functions/certificateMonitor/src/certificateThreshold.ts'
      - 'packages/core/src/**'
      - 'sst.config.ts'
      - 'packages/functions/certificateMonitor/tsconfig.json'
  workflow_dispatch:
    inputs:
      release_number_override:
        required: false
        type: string

permissions:
  id-token: write
  packages: read
  contents: read

jobs:
  build_lambda_function:
    uses: ./.github/workflows/build-lambda.yml
    with:
      lambda_name: lambda-pago-certificate-monitor
      function: packages/functions/certificateMonitor/src/certificateThreshold.main
      release_number: ${{inputs.release_number_override || vars.ICS_RELEASE_NUMBER}}
    secrets:
      JF_ACCESS_TOKEN: ${{ secrets.JF_ACCESS_TOKEN }}
