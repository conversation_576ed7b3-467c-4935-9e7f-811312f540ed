---
# Reusable workflow that creates a generic type build (zip) using SST Framework
name: Build Lambda Function (SST)

on:
  workflow_call:
    inputs:
      lambda_name:
        required: true
        type: string
      function:
        required: true
        type: string
      release_number:
        required: true
        type: string
      add_path_to_artifact:
        required: false
        type: string
    secrets:
      JF_ACCESS_TOKEN:
          required: true

jobs:
  build_lambda_function:
    runs-on: [self-hosted, linux]

    container:
      image: ghcr.io/invenco-cloud-systems-ics/ics-images/os-al2-generic:latest
      credentials:
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    env:
      SST_TELEMETRY_DISABLED: "1"
      BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
      BUILD_DIR: ".sst/dist"

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - uses: pnpm/action-setup@v2
        with:
          version: 8

      - uses: actions/setup-node@v3    
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          node-version: 18
          registry-url: 'https://npm.pkg.github.com'

      - name: Configure AWS Credentials # Needed by SST to build lambdas
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::682239886782:role/iam-github-workflows-sst-role
          aws-region: ap-southeast-2

      - name: Setup Artifactory
        uses: jfrog/setup-jfrog-cli@v3
        env:
          JF_URL: https://invenco.jfrog.io/
          JF_ACCESS_TOKEN: ${{ secrets.JF_ACCESS_TOKEN }}

      - name: Set environment variables
        run: |
          N2_VERSION=$(date +%y%m%d%H%M%S)
          F_VERSION=${{ inputs.release_number }}-${{ env.BRANCH_NAME }}-${N2_VERSION}.${{ github.run_attempt }}

          # Package variables
          ARTIFACT=${{ inputs.lambda_name }}-${F_VERSION}.zip
          echo "ARTIFACT=${ARTIFACT}" >> $GITHUB_ENV

          # Artifactory variables
          REPOSITORY=ics-${{ inputs.lambda_name }}-generic-dev
          PROJECT=ics
          BUILD_NAME=${{ inputs.release_number }}
          BUILD_NUMBER=${N2_VERSION}.${{ github.run_attempt }}
          echo "REPOSITORY=${REPOSITORY}" >> $GITHUB_ENV
          echo "PROJECT=${PROJECT}" >> $GITHUB_ENV
          echo "BUILD_NAME=${BUILD_NAME}" >> $GITHUB_ENV
          echo "BUILD_NUMBER=${BUILD_NUMBER}" >> $GITHUB_ENV

      - name: Install Dependencies
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: pnpm install --no-frozen-lockfile

      # - name: Typecheck
      #   run: npm run typecheck

      - name: Build
        env:
          # Soft-limit memory for esbuild to prevent OOM situation
          GOMEMLIMIT: "3500MiB"
        run: npm run build -- --stage package --to ${{ env.BUILD_DIR }}

      - name: Package
        run: |
          # Asset will have .mjs extension instead of the original ts, so we change it here
          ASSET=$(echo "${{ env.BUILD_DIR }}/*/${{ inputs.function }}" | sed 's/\.ts$/.mjs/')
          TEMP=$(mktemp -d)

          cp ${ASSET} ${TEMP}/index.mjs
          zip -r -q -j ${{ env.ARTIFACT }} ${TEMP}/index.mjs

      - name: Add extra files to artifact
        if: ${{ inputs.add_path_to_artifact }}
        run: zip -r -q ${{ env.ARTIFACT }} ${{ inputs.add_path_to_artifact }}

      - name: Upload to Artifactory
        run: jf rt u ${{ env.ARTIFACT }} ${{ env.REPOSITORY }} --detailed-summary --project=${{ env.PROJECT }} --build-name=${{ env.BUILD_NAME }} --build-number=${{ env.BUILD_NUMBER }}

      - name: Publish Build to Artifactory
        run: jf rt bp ${{ env.BUILD_NAME }} ${{ env.BUILD_NUMBER }} --project=${{ env.PROJECT }}
