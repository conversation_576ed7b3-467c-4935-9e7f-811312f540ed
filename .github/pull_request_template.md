*Please keep the checklist that applies to the changes you're making*

# Code Review Checklist (DB)
*To be completed by the PR author*

* [x] Check items which are applicable

~* [ ]  Strikethrough items which are not applicable~

---
Requirements:
* [ ]  Have the requirements been met?
* [ ]  Have stakeholder(s) approved the change? (to be completed by lead/codeowner/tester)

Maintainability:
* [ ]  Is the change easy to track?

Testing:
* [ ]  Does the deployment work as expected?
* [ ]  Is the change validated in a dev environment?

Code Formatting:
* [ ]  Is the code formatted correctly?
* [ ]  Unnecessary whitespace removed?

Performance:
* [ ]  Is the code performance acceptable?

# Code Review Checklist (API)
*To be completed by the PR author*

* [x] Check items which are applicable

~* [ ]  Strikethrough items which are not applicable~

---
Requirements:
* [ ]  Have the requirements been met?
* [ ]  Have stakeholder(s) approved the change? (to be completed by lead/codeowner/tester)

Maintainability:
* [ ]  Is the change easy to track?
* [ ]  Is the code not repeated (D.R.Y. Principle)?
* [ ]  Is the code method/class not too long?

Testing:
* [ ]  Does the deployment work as expected?
* [ ]  Are the test cases dev tested in a test environment?
* [ ]  Have edge cases been tested?
* [ ]  Are invalid inputs validated?
* [ ]  Are inputs sanitised?

Code Formatting:
* [ ]  Linting errors corrected?

Performance:
* [ ]  Is the code performance acceptable?

Documentation:
* [ ]  Is there sufficient documentation?
* [ ]  Is the ReadMe.md file up to date?
* [ ]  Is the swagger doc up to date? (for APIs)

Best Practices:
* [ ]  Follow Single Responsibility principle?
* [ ]  Are different errors handled correctly?
* [ ]  Are errors and warnings logged?
* [ ]  Minimal nesting used?
* [ ]  `package-lock.json` should be committed if there are any changes

Architecture:
* [ ]  Is it secure/free from risk (NPM audit, SQL injection, OWASP Top 10, etc)?
* [ ]  Are separations of concerns followed?
* [ ]  Relevant Parameters are configurable?
* [ ]  Leveraged feature flags if applicable?