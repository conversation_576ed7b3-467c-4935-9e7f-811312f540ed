/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable no-console */
const { readdir, mkdir } = require('fs/promises');
const path = require('path');
const pc = require('picocolors');

const { ask } = require('../../ask');

const cwd = process.cwd();
const MIGRATIONS_FOLDER_PATH = path.join(cwd, './packages/core/src/migrations');
const addNewServiceOption = 'Add a new service...';

module.exports = {
  prompt: async ({ prompter, args }) => {
    try {
      const services = await readdir(MIGRATIONS_FOLDER_PATH);
      let newServiceName;

      const existingServiceName = await ask({
        name: 'seviceName',
        type: 'select',
        message: 'Choose a service:',
        choices: [...services, addNewServiceOption],
      })({ prompter, args });

      if (existingServiceName === addNewServiceOption) {
        newServiceName = await ask({
          name: 'newSeviceName',
          type: 'input',
          message: 'Enter the name for a new service:',
          validate: value =>
            /^([A-Za-z])+$/.test(value)
              ? true
              : 'Service name may only include letters. No spaces. Use camel case.',
        })({ prompter, args });

        await mkdir(path.join(MIGRATIONS_FOLDER_PATH, newServiceName));

        console.log(
          `✨ The new service folder: ${pc.green(
            newServiceName
          )} was successfully created inside migrations. ✨`
        );
      }

      const migrationName = await ask({
        name: 'migrationName',
        type: 'input',
        message: 'Enter a migration name:',
      })({ prompter, args });

      return {
        seviceName: newServiceName
          ? newServiceName.charAt(0).toLowerCase() + newServiceName.slice(1)
          : existingServiceName,
        migrationName: migrationName.split(' ').join('_').toLowerCase(),
        migrationFolderPath: MIGRATIONS_FOLDER_PATH,
      };
    } catch (e) {
      console.error('Error', e);
    }
    return {};
  },
};
