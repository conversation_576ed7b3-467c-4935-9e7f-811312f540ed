{"name": "ics-config-file-status", "version": "0.0.1", "private": true, "author": "Invenco", "scripts": {"prepare": "husky install", "dev": "sst dev", "build": "sst build", "deploy": "sst deploy", "delete": "sst remove", "console": "sst console", "typecheck": "tsc --noEmit", "lint": "eslint '**/*.{js,ts}' --max-warnings=100 --cache --fix", "gen": "hygen", "test": "sst bind vitest run"}, "lint-staged": {"*.+(js|ts)": ["pnpm lint"], "*.+(js|ts|json|md)": ["prettier -c --write"]}, "engines": {"node": ">=18", "pnpm": ">=7"}, "dependencies": {"@aws-sdk/client-ses": "^3.568.0", "aws-cdk-lib": "2.132.1", "axios": "^1.6.7", "constructs": "10.3.0", "sst": "2.41.5"}, "devDependencies": {"@invenco-cloud-systems-ics/eslint-config": "2.2.1", "@tsconfig/node18": "^18.2.4", "@types/aws-lambda": "^8.10.137", "@types/jsonwebtoken": "^9.0.6", "@types/pg": "^8.11.5", "eslint": "^8.46.0", "husky": "^8.0.3", "hygen": "^6.2.11", "lint-staged": "^13.2.3", "picocolors": "^1.0.0", "typescript": "^5.4.5", "vitest": "^1.5.3"}, "workspaces": ["packages/*"]}