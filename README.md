# ICS-LAMDA-CSR-SERVICES

Project is using SST framework [more info](https://sst.dev)

## Run Locally

### Dependencies

- pnpm: [Installation guide](https://pnpm.io/installation)

### Create .env.local file in root directory and add the following params:

```dotenv
ENVIRONMENT=martini
GET_SITES_RULE_PRIORITY=<number>
GET_MISMATCH_RULE_PRIORITY=<number>
GET_MISMATCH_FILE_RULE_PRIORITY=<number>
GET_API_DOC_RULE_PRIORITY=<number>
GET_SWAGGER_UI_RULE_PRIORITY=<number>
PUT_UPDATE_DEPLOYMENT_TYPE=<number>
MAINTENANCE_TIME_FROM=<hh:mm>
MAINTENANCE_TIME_TO=<hh:mm>
ENABLE_CRON=<boolean>
```

### Install Node.js modules

Highly recommend to use PNPM instead of NPM as packet manager.

```sh
pnpm install
```

### Run locally

Run the following for local development:

```sh
pnpm dev
```

For database connection through bastion use:

```sh
ssh -NL 5432:db-config-file-status-martini.invencocloud.com:5432 <your account>@52.43.116.81
```

### Remove stack from AWS

Run the following

```sh
pnpm delete
```

## Build

GitHub Actions are configured to run for each Lambda when a Pull Request is created/updated and when it is merged. The build artifacts are published to Artifactory and can then be deployed from Harness.
