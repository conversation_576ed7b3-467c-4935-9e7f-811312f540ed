import { SSTConfig } from 'sst';

import { Pago } from './stacks/Pago';

export default {
  config(_input) {
    return {
      name: 'ICS',
      region: 'ap-southeast-2',
    };
  },
  stacks(app) {
    app.setDefaultFunctionProps({
      environment: {
        POWERTOOLS_DEV: process.env.POWERTOOLS_DEV!,
        ENVIRONMENT: process.env.ENVIRONMENT!,
      },
      runtime: 'nodejs18.x',
      timeout: 60,
      memorySize: 512,
      nodejs: {
        esbuild: {
          external: ['pg-native'],
          alias: {
            '@ics/core': './packages/core/src',
          },
        },
      },
    });

    app.addDefaultFunctionPermissions('*');

    app.stack(Pago, { id: 'Pago' });
  },
} satisfies SSTConfig;
