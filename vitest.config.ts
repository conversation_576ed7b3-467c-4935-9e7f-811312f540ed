/// <reference types="vitest" />

import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    testTimeout: 30000,
    globals: true,
    useAtomics: true,
  },
  logLevel: 'info',
  esbuild: {
    sourcemap: 'both',
  },
  resolve: {
    alias: {
      '@ics/core/query': './packages/core/src/query',
      '@ics/core/middleware': './packages/core/src/middleware',
      '@ics/core': './packages/core/src',
    },
  },
});
