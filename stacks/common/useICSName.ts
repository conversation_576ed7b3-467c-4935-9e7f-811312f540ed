export const useICSName = (stage: string) => {
  const getLambdaName = (name: string) => `ics-lambda-${name}-${stage}`;
  const getQueueName = (name: string) => `${stage}-${name}-queue`;

  const getRuleName = (name: string) => `${stage}-${name}`;

  const getRuleNames = (name: string) => ({
    functionName: getLambdaName(name),
    queueName: getQueueName(name),
    ruleName: getRuleName(name),
  });

  return { getLambdaName, getQueueName, getRuleName, getRuleNames };
};
