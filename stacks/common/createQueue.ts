import { Queue, FunctionProps, StackContext } from 'sst/constructs';
import * as lambdaEventSources from 'aws-cdk-lib/aws-lambda-event-sources';

export type QueueArgs = {
  stack: StackContext['stack'];
  handlerPath: string;
  queueId: string;
  queueName?: string;
  eventSource?: lambdaEventSources.SqsEventSourceProps;
} & FunctionProps;

export const createQueue = ({
  stack,
  queueId,
  queueName,
  handlerPath,
  eventSource,
  ...rest
}: QueueArgs) =>
  new Queue(stack, queueId, {
    consumer: {
      function: {
        handler: `packages/functions/${handlerPath}`,
        ...rest,
      },
      cdk: {
        eventSource,
      },
    },
    cdk: {
      queue: {
        queueName,
      },
    },
  });
