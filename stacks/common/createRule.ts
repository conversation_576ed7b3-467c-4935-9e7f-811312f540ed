import { createQueue, type QueueArgs } from './createQueue';

type Args = {
  detailType: string[];
  ruleName?: string;
  detail?: Record<string, unknown>;
} & QueueArgs;

export const createRule = ({
  detailType,
  ruleName,
  detail,
  ...rest
}: Args) => ({
  pattern: {
    detailType,
    detail,
  },
  targets: {
    queue: createQueue(rest),
  },
  cdk: {
    rule: {
      ruleName,
    },
  },
});
