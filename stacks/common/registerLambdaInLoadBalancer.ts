import {
  ApplicationTargetGroup,
  type IApplicationListener,
  type IApplicationLoadBalancerTarget,
  ListenerAction,
  ListenerCondition,
} from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import { LambdaTarget } from 'aws-cdk-lib/aws-elasticloadbalancingv2-targets';
import { StackContext, Function } from 'sst/constructs';

export const registerLambdaInLoadBalancer = ({
  stack,
  lambda,
  applicationListener,
  path,
  methods = ['GET', 'OPTIONS'],
  priority,
}: {
  stack: StackContext['stack'];
  lambda: Function;
  applicationListener: IApplicationListener;
  path: string;
  methods?: string[];
  priority: number;
}) => {
  const targetGroup = new ApplicationTargetGroup(
    stack,
    `${lambda.id}TargetGroup`,
    {
      targets: [
        new LambdaTarget(
          lambda as any
        ) as unknown as IApplicationLoadBalancerTarget,
      ],
      healthCheck: {
        enabled: false,
      },
    }
  );

  targetGroup.setAttribute('lambda.multi_value_headers.enabled', 'true');

  applicationListener.addAction(`${lambda.id}AlbRedirectAction`, {
    action: ListenerAction.forward([targetGroup]),
    conditions: [
      ListenerCondition.pathPatterns([path]),
      ListenerCondition.httpRequestMethods(methods),
    ],
    priority,
  });
};
