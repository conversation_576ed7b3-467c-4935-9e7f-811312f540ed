import { StackContext, Function, Cron } from 'sst/constructs';

import { useICSName } from './common/useICSName';

export function Pago({ stack }: StackContext) {
  const { getLambdaName } = useICSName(stack.stage);

  // Certificate monitoring lambda function
  const certificateMonitorLambda = new Function(stack, 'certificateMonitorLambda', {
    functionName: getLambdaName('certificate-monitor'),
    handler: 'packages/functions/certificateMonitor/src/certificateThreshold.main',
    runtime: 'nodejs18.x',
    timeout: 60, // 1 minute timeout
    memorySize: 512, // More memory for API calls
    permissions: ['ses:SendEmail', 'ses:SendRawEmail'], // Permissions for sending emails
  });

  // Schedule the certificate monitoring lambda to run every hour
  // eslint-disable-next-line no-new
  new Cron(stack, 'certificateMonitorCron', {
    schedule: 'rate(1 minute)',
    job: certificateMonitorLambda,
    enabled: true,
  });

  stack.addOutputs({
    CertificateMonitorLambda: certificateMonitorLambda.functionName,
  });

}
