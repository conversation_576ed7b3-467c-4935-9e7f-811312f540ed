# Multiple Database Support

This document explains how to use the enhanced database functionality that supports multiple databases in the ICS Lambda CSR Services project.

## Overview

The system now supports connecting to multiple databases:
- **ICSDB**: Main database containing certificate references, CSR requests, and user data
- **AssetManagement**: Asset management database containing asset information

## Database Configuration

The databases are configured in the `AppConfig` type:

```typescript
export type AppConfig = {
  ICSDB: {
    name: string;
    host: string;
    userName: string;
    password: string;
  };
  AssetManagement: {
    name: string;
    host: string;
    userName: string;
    password: string;
  };
  // ... other config
};
```

## Usage Methods

### 1. Specific Database Functions

Use dedicated functions for each database:

```typescript
import { useDB, useAssetDB } from '@ics/core/db';

// Connect to ICSDB
const icsDb = useDB(appConfig);
const users = await icsDb.selectFrom('user').selectAll().execute();

// Connect to Asset Management DB
const assetDb = useAssetDB(appConfig);
const csrDevices = await assetDb.selectFrom('csrRequestDevice').selectAll().execute();
```

### 2. Generic Database Function

Use the generic `useDatabase` function:

```typescript
import { useDatabase, DatabaseName } from '@ics/core/db';

// Connect to any database
const db = useDatabase(appConfig, 'ICSDB');
const db2 = useDatabase(appConfig, 'AssetManagement');
```

### 3. Enhanced useTable Function

Use `useTable` with database selection:

```typescript
import { useTable } from '@ics/core/db';

// Use table from ICSDB
const { db, selectFromTable } = useTable(appConfig, {
  tableName: 'certificateReferences',
  databaseName: 'ICSDB'
});

// Use table from Asset Management DB
const { db: assetDb, selectFromTable: assetTable } = useTable(appConfig, {
  tableName: 'csrRequestDevice',
  databaseName: 'AssetManagement'
});
```

### 4. Convenience Functions

Use convenience functions for better readability:

```typescript
import { useICSDB, useAssetManagementDB } from '@ics/core/db';

const icsDb = useICSDB(appConfig);
const assetDb = useAssetManagementDB(appConfig);
```

## Database Schemas

### ICSDB Schema
- `certificateReferences`: Certificate reference records
- `csrRequestDevice`: CSR request device information
- `csrRequestSession`: CSR request session data
- `user`: User information

### AssetManagement Schema
- `csrRequestDevice`: CSR request device information
- `csrRequestSession`: CSR request session data

## Features

### Connection Pooling
Each database maintains its own connection pool with optimized settings:
- Connection timeout: 10 seconds
- Max connections: 1 per Lambda instance
- Idle timeout: 0 (connections don't idle)

### SSL Configuration
SSL is automatically configured based on the host:
- **Local hosts** (localhost, 127.0.0.1): SSL disabled
- **Remote hosts**: SSL enabled with `rejectUnauthorized: false`

### Connection Caching
Database instances are cached to avoid creating multiple connections:
- One instance per database type per Lambda container
- Instances are reused across function invocations

### Type Safety
Full TypeScript support with proper typing:
- Database-specific table types
- Compile-time table name validation
- IntelliSense support for all operations

## Migration from Single Database

### Backward Compatibility
Existing code continues to work without changes:
```typescript
// This still works
const db = useDB(appConfig);
```

### Updating Existing Code
To use multiple databases, update imports and specify the database:
```typescript
// Before
const { db } = useTable(appConfig, { tableName: 'certificateReferences' });

// After (explicit database selection)
const { db } = useTable(appConfig, {
  tableName: 'certificateReferences',
  databaseName: 'ICSDB'
});
```

## Best Practices

1. **Use specific functions** when you know which database you need
2. **Use generic functions** for dynamic database selection
3. **Cache database instances** at the module level when possible
4. **Handle connection errors** gracefully
5. **Use transactions** within a single database only
6. **Close connections** when shutting down (use `closeAllDatabases()`)

## Error Handling

The system throws descriptive errors for common issues:
- Missing database configuration
- Unsupported database names
- Connection failures

```typescript
try {
  const db = useDatabase(appConfig, 'InvalidDB' as DatabaseName);
} catch (error) {
  console.error('Database error:', error.message);
  // Error: Database configuration for 'InvalidDB' not found in AppConfig
}
```
