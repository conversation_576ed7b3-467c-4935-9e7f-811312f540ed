{"name": "@ics-config-file-status/core", "version": "0.0.0", "type": "module", "scripts": {"test": "sst bind vitest", "typecheck": "tsc -noEmit"}, "dependencies": {"@aws-lambda-powertools/logger": "^1.12.1", "@aws-lambda-powertools/metrics": "^1.12.1", "@aws-lambda-powertools/tracer": "^1.12.1", "@aws-sdk/client-ses": "^3.568.0", "@invenco-cloud-systems-ics/ics-library-appconfig": "2.2.1", "@middy/core": "^4.5.5", "@middy/do-not-wait-for-empty-event-loop": "^4.5.5", "axios": "^1.6.8", "kysely": "^0.27.3", "pg": "^8.11.5"}, "devDependencies": {"@types/http-errors": "^2.0.4", "@types/lodash": "^4.17.0", "@types/qs": "^6.9.15"}}