import axios from 'axios';

type Parameter = {
  Name: string;
  Value: string;
};

type ExtensionResponse = {
  Parameter: Parameter;
};

const AWS_SECRETS_HTTP_PORT = 2773;
const AWS_SECRETS_SERVER_ENDPOINT = `http://localhost:${AWS_SECRETS_HTTP_PORT}/systemsmanager/parameters/get`;

export const getParameterFromSSM = async (name: string) => {
  const {
    data: {
      Parameter: { Value },
    },
  } = await axios.get<ExtensionResponse>(AWS_SECRETS_SERVER_ENDPOINT, {
    headers: {
      'X-Aws-Parameters-Secrets-Token': process.env.AWS_SESSION_TOKEN,
    },
    params: {
      name,
      withDecryption: true,
    },
  });

  return Value;
};
