/* eslint-disable no-param-reassign */
import createHttpError from 'http-errors';

import { CustomALBEvent } from '../types';

type BeforeEvent = {
  event: CustomALBEvent<unknown>;
};

const ACCEPTABLE_ROLES = [
  'CONFIG_MGMT_PUBLISH',
  'CONFIG_MGMT_ASSIGN',
  'CONFIG_MGMT_DEPLOY',
];
const FEATURE_FLAG = 'CONFIG_MGMT';
const COMPANY_ADMIN = 'COMPANY_ADMIN';
const SYSTEM_USER = 'ICS_SYSTEM';
export const userPermissionCheck = () => {
  const beforeHook = async ({ event }: BeforeEvent) => {
    if (event.httpMethod !== 'OPTIONS') {
      const isUserHaveRole = event.auth.payload.roles.some((role: string) =>
        [...ACCEPTABLE_ROLES, SYSTEM_USER].includes(role)
      );
      event.auth.payload.isCompanyAdmin =
        event.auth.payload.roles.includes(COMPANY_ADMIN);
      const isUserHaveFeatureFlag =
        event.auth.payload.company?.featureFlags?.includes(FEATURE_FLAG);
      const isSystemUser = event.auth.payload.roles.some(
        role => role === SYSTEM_USER
      );
      event.auth.payload.isSystemUser = isSystemUser;
      if (!isUserHaveRole) {
        throw createHttpError(
          403,
          'User is not authorized for this operation',
          {
            type: 'NotAuthorized',
          }
        );
      }
      if (!isUserHaveFeatureFlag && !isSystemUser) {
        throw createHttpError(
          403,
          'User does not have a required feature flag',
          {
            type: 'NotAuthorized',
          }
        );
      }
    }
  };

  return {
    before: beforeHook,
  };
};
