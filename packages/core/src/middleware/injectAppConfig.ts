/* eslint-disable no-param-reassign */
import { getAppConfig } from '@invenco-cloud-systems-ics/ics-library-appconfig';

import { AppConfig } from '../types';

const applicationIdentifier =
  process.env.APPCONFIG_IDENTIFIER || 'ics-lambda-rnds';
const environment = process.env.ENVIRONMENT || 'martini';
console.log("----------------------------------------------", environment)
const configProfile = 'default';
const appConfigCacheMinutes =
  Number(process.env.APP_CONFIG_CACHE_MINUTES) || 60;
const appConfigCacheExp = 60 * 1000 * appConfigCacheMinutes;

const appParams = {
  ApplicationIdentifier: applicationIdentifier,
  ConfigurationProfileIdentifier: `${environment}-${configProfile}`,
  EnvironmentIdentifier: environment,
};

type BeforeEvent = {
  event: any;
};

type ConfigCache = {
  value: null | AppConfig;
  exp: number;
};

const configCache: ConfigCache = {
  value: null,
  exp: 0,
};

export const injectAppConfig = () => {
  const beforeHook = async ({ event }: BeforeEvent) => {
    if (!configCache.value || configCache.exp < Date.now()) {
      const config = (await getAppConfig({
        aws: { appParams },
        local: !!process.env.IS_LOCAL ?? false,
      })) as AppConfig;

      event.appConfig = config;

      configCache.exp = Date.now() + appConfigCacheExp;
      configCache.value = config;
    } else {
      event.appConfig = configCache.value;
    }
  };

  return {
    before: beforeHook,
  };
};
