/* eslint-disable no-param-reassign */
import { invokeLambda } from '@ics/core/invokeLambda';
import { requestClient } from '@ics/core/requestClient';
import { AxiosInstance } from 'axios';
import { decode } from 'jsonwebtoken';

import { CustomSQSEvent } from '../types';

type BeforeEvent = {
  event: CustomSQSEvent<{ tenantId: string }>;
};

const TEN_MINUTES_IN_MS = 600000;

let axiosInstance: AxiosInstance | null = null;
let token = '';

const getToken = async (authorizerLambdaName: string) => {
  const textDecoder = new TextDecoder();
  const { token: newToken } = JSON.parse(
    textDecoder.decode(
      (await invokeLambda({
        functionName: authorizerLambdaName,
      })) as Uint8Array
    )
  );

  return newToken;
};

export const injectAxiosInstance = () => {
  const beforeHook = async ({ event }: BeforeEvent) => {
    if (!axiosInstance) {
      token = await getToken(event.appConfig.Lambdas.authorizerJwt);
      axiosInstance = requestClient({
        baseURL: event.appConfig.API.baseUrl,
        token,
        authorizerLambdaName: event.appConfig.Lambdas.authorizerJwt,
      });
    } else {
      const { exp = 0 } = decode(token, { json: true }) || {};
      if (Date.now() > exp * 1000 - TEN_MINUTES_IN_MS) {
        token = await getToken(event.appConfig.Lambdas.authorizerJwt);
        axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`;
      }
    }

    event.axiosInstance = axiosInstance;
  };

  return {
    before: beforeHook,
  };
};
