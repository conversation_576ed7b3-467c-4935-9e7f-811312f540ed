import qs, { IParseOptions } from 'qs';

import type { ALBResult } from 'aws-lambda';

import { CustomALBEvent } from '../types';

type BeforeEvent = {
  event: CustomALBEvent<unknown>;
};

type AfterEvent = {
  response: ALBResult;
};

export function isObject(object: unknown) {
  return !!object && object.constructor === Object;
}

export interface MultiValueQueryStringParameters {
  [name: string]: string[] | Record<string, unknown>;
}

const flattenQueryParams = (
  obj?: MultiValueQueryStringParameters
): Record<string, unknown> => {
  if (!obj) {
    return {};
  }
  return Object.keys(obj).reduce((acc, key) => {
    if (isObject(obj[key])) {
      return {
        ...acc,
        [key]: flattenQueryParams(obj[key] as MultiValueQueryStringParameters),
      };
    }

    return Array.isArray(obj[key]) && obj[key].length === 1
      ? { ...acc, [key]: (obj[key] as string[])[0] }
      : { ...acc, [key]: obj[key] };
  }, {});
};

export const albHeaderNormalizer = (options: IParseOptions = {}) => {
  const albHeaderNormalizerMiddlewareBefore = async ({
    event,
  }: BeforeEvent) => {
    const multiValueQueryParams = qs.parse(
      qs.stringify(event.multiValueQueryStringParameters),
      options
    );
    // eslint-disable-next-line no-param-reassign
    event.queryStringParameters = flattenQueryParams(
      multiValueQueryParams as MultiValueQueryStringParameters
    );

    if (event.multiValueHeaders) {
      // eslint-disable-next-line no-param-reassign
      event.headers = Object.entries(event.multiValueHeaders).reduce(
        (acc, [key, value]) => ({
          ...acc,
          [key]: Array.isArray(value) ? value[0] : value,
        }),
        {}
      );
    }
  };

  const albHeaderNormalizerMiddlewareAfter = ({ response }: AfterEvent) => {
    if (response?.headers) {
      response.multiValueHeaders = Object.entries(response.headers).reduce(
        (acc, [key, value]) => ({ ...acc, [key]: [value] }),
        {}
      );
    }
  };

  return {
    before: albHeaderNormalizerMiddlewareBefore,
    after: albHeaderNormalizerMiddlewareAfter,
    onError: albHeaderNormalizerMiddlewareAfter,
  };
};
