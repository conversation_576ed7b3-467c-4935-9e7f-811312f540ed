/**
 * Example usage of multiple database support
 * This file demonstrates how to use the enhanced database functions
 */

import { 
  useDB, 
  useAssetDB, 
  useDatabase, 
  useTable, 
  useICSDB, 
  useAssetManagementDB,
  DatabaseName 
} from '../db';
import { AppConfig } from '../types';

// Example function showing different ways to connect to databases
export async function exampleMultiDatabaseUsage(appConfig: AppConfig) {
  
  // Method 1: Using specific database functions
  console.log('=== Method 1: Specific Database Functions ===');
  
  // Connect to ICSDB (main database)
  const icsDb = useDB(appConfig);
  const certificateReferences = await icsDb
    .selectFrom('certificateReferences')
    .selectAll()
    .limit(5)
    .execute();
  console.log('Certificate references:', certificateReferences.length);

  // Connect to Asset Management database
  const assetDb = useAssetDB(appConfig);
  const assets = await assetDb
    .selectFrom('asset')
    .selectAll()
    .limit(5)
    .execute();
  console.log('Assets:', assets.length);

  // Method 2: Using generic database function
  console.log('=== Method 2: Generic Database Function ===');
  
  // Connect to ICSDB using generic function
  const icsDbGeneric = useDatabase(appConfig, 'ICSDB');
  const users = await icsDbGeneric
    .selectFrom('user')
    .select(['id', 'email', 'fullName'])
    .limit(5)
    .execute();
  console.log('Users:', users.length);

  // Connect to Asset Management using generic function
  const assetDbGeneric = useDatabase(appConfig, 'AssetManagement');
  const assetCategories = await assetDbGeneric
    .selectFrom('assetCategory')
    .selectAll()
    .limit(5)
    .execute();
  console.log('Asset categories:', assetCategories.length);

  // Method 3: Using useTable with database selection
  console.log('=== Method 3: useTable with Database Selection ===');
  
  // Use table from ICSDB
  const { db: icsTableDb, selectFromTable: icsTable } = useTable(appConfig, {
    tableName: 'certificateReferences',
    databaseName: 'ICSDB'
  });
  
  if (icsTable) {
    const certRefs = await icsTable.selectAll().limit(3).execute();
    console.log('Certificate references via useTable:', certRefs.length);
  }

  // Use table from Asset Management database
  const { db: assetTableDb, selectFromTable: assetTable } = useTable(appConfig, {
    tableName: 'asset',
    databaseName: 'AssetManagement'
  });
  
  if (assetTable) {
    const assetsViaTable = await assetTable.selectAll().limit(3).execute();
    console.log('Assets via useTable:', assetsViaTable.length);
  }

  // Method 4: Using convenience functions
  console.log('=== Method 4: Convenience Functions ===');
  
  const icsDbConvenience = useICSDB(appConfig);
  const csrDevices = await icsDbConvenience
    .selectFrom('csrRequestDevice')
    .select(['deviceId', 'status'])
    .limit(5)
    .execute();
  console.log('CSR devices:', csrDevices.length);

  const assetDbConvenience = useAssetManagementDB(appConfig);
  const locations = await assetDbConvenience
    .selectFrom('assetLocation')
    .selectAll()
    .limit(5)
    .execute();
  console.log('Asset locations:', locations.length);
}

// Example of dynamic database selection
export async function dynamicDatabaseExample(appConfig: AppConfig, dbName: DatabaseName) {
  console.log(`=== Dynamic Database Selection: ${dbName} ===`);
  
  const db = useDatabase(appConfig, dbName);
  
  if (dbName === 'ICSDB') {
    const count = await db
      .selectFrom('user')
      .select(db.fn.count('id').as('userCount'))
      .executeTakeFirst();
    console.log('User count:', count?.userCount);
  } else if (dbName === 'AssetManagement') {
    const count = await db
      .selectFrom('asset')
      .select(db.fn.count('id').as('assetCount'))
      .executeTakeFirst();
    console.log('Asset count:', count?.assetCount);
  }
}

// Example of transaction across single database
export async function transactionExample(appConfig: AppConfig) {
  console.log('=== Transaction Example ===');
  
  const db = useDB(appConfig);
  
  try {
    await db.transaction().execute(async (trx) => {
      // Update certificate reference
      await trx
        .updateTable('certificateReferences')
        .set({ updatedDate: new Date() })
        .where('id', '=', 1)
        .execute();
      
      // Insert or update user
      await trx
        .updateTable('user')
        .set({ lastLocked: new Date() })
        .where('id', '=', 'some-user-id')
        .execute();
      
      console.log('Transaction completed successfully');
    });
  } catch (error) {
    console.error('Transaction failed:', error);
  }
}
