import axios, { AxiosError, AxiosInstance } from 'axios';
import * as rax from 'retry-axios';
import { invokeLambda } from '@ics/core/invokeLambda';
import qs from 'qs';

type Props = {
  token: string;
  baseURL: string;
  authorizerLambdaName: string;
};

const HTTP_METHODS_TO_RETRY = [
  'GET',
  'POST',
  'HEAD',
  'PUT',
  'OPTIONS',
  'DELETE',
];

export const requestClient = ({
  token,
  baseURL,
  authorizerLambdaName,
}: Props): AxiosInstance => {
  const axiosInstance = axios.create({
    baseURL,
    headers: {
      common: {
        Authorization: `Bearer ${token}`,
      },
    },
    paramsSerializer: {
      serialize: params =>
        qs.stringify(params, {
          arrayFormat: 'brackets',
          encode: false,
        }),
    },
    timeout: 240000,
  });

  const refreshToken = async (error: AxiosError) => {
    if (error?.response?.status !== 401) {
      return;
    }
    const response = await invoke<PERSON>ambda({
      functionName: authorizerLambdaName,
    });
    const textDecoder = new TextDecoder();
    const { token: newToken } = JSON.parse(
      textDecoder.decode(response as Uint8Array)
    );
    axiosInstance.defaults.headers.common.Authorization = `Bearer ${newToken}`;
    // eslint-disable-next-line no-param-reassign
    error!.config!.headers!.Authorization = `Bearer ${newToken}`;
  };

  axiosInstance.defaults.raxConfig = {
    retry: 3,
    backoffType: 'exponential',
    httpMethodsToRetry: HTTP_METHODS_TO_RETRY,
    statusCodesToRetry: [
      [401, 401],
      [429, 429],
    ],
    onRetryAttempt: refreshToken,
    instance: axiosInstance,
  };

  rax.attach(axiosInstance);

  return axiosInstance;
};
