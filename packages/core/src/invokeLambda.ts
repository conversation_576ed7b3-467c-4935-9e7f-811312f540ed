import {
  LambdaClient,
  InvokeCommand,
  InvocationType,
} from '@aws-sdk/client-lambda';

type InvokeProps = {
  functionName: string;
  isEvent?: boolean;
  payload?: Uint8Array;
};

const lambdaClient = new LambdaClient({
  retryMode: 'adaptive',
  maxAttempts: 5,
});

export async function invokeLambda({
  functionName,
  isEvent,
  payload,
}: InvokeProps) {
  const invokeCommand = new InvokeCommand({
    FunctionName: functionName,
    InvocationType: isEvent
      ? InvocationType.Event
      : InvocationType.RequestResponse,
    Payload: payload,
  });

  const response = await lambdaClient.send(invokeCommand);

  if (isEvent) {
    return response;
  }

  return response.Payload;
}
