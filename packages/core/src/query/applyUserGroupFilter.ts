import { SelectQueryBuilder } from 'kysely';
import { Database } from '@ics/core/types';

type Options = {
  userId: string;
  isCompanyAdmin?: boolean;
};

export const applyUserGroupFilter = (
  db: SelectQueryBuilder<Database, keyof Database, {}>,
  { userId, isCompanyAdmin }: Options
) => {
  if (!isCompanyAdmin) {
    return db
      .innerJoin(
        'authorizationSiteGroupSite',
        'authorizationSiteGroupSite.siteId',
        'configFileDeviceStatus.siteId'
      )
      .innerJoin(
        'userGroupAuthorizationSiteGroup',
        'userGroupAuthorizationSiteGroup.authorizationSiteGroupId',
        'authorizationSiteGroupSite.authorizationSiteGroupId'
      )
      .innerJoin(
        'userGroupUser',
        'userGroupUser.userGroupId',
        'userGroupAuthorizationSiteGroup.userGroupId'
      )
      .where('userGroupUser.userId', '=', userId);
  }
  return db;
};
