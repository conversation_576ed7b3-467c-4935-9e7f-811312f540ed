import {
  ComparisonOperatorExpression,
  ReferenceExpression,
  SelectQueryBuilder,
} from 'kysely';

import { Filter, Operator } from '../types';
import { createError } from '../createError';

const getKyselyOperator = (
  operator: Operator
): ComparisonOperatorExpression => {
  switch (operator) {
    case '$eq': {
      return '=';
    }
    case '$in': {
      return 'in';
    }
    default: {
      throw createError({
        code: 400,
        message: `Operator ${operator} does not supported. Supported operators: [$in, $eq]`,
      });
    }
  }
};

export const applyFilters = <
  D,
  K extends keyof D,
  Table extends {},
  S extends {},
>(
  db: SelectQueryBuilder<D, K, S>,
  filters: Filter<keyof Table>
) =>
  (Object.keys(filters) as Array<keyof typeof filters>).reduce((query, key) => {
    const operators = Object.keys(filters[key]) as Array<
      keyof (typeof filters)[keyof Table]
    >;
    return operators.reduce((accQuery, operator) => {
      const value = filters[key][operator];
      return accQuery.where(
        `configFileDeviceStatus.${String(key)}` as ReferenceExpression<D, K>,
        getKyselyOperator(operator as Operator),
        typeof value === 'string' ? [value] : value
      );
    }, query);
  }, db);
