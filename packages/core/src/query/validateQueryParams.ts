import { createError } from '../createError';

const DEFAULT_QUERY_PARAMS_WHITELIST = [
  'onlyMeta',
  'filters',
  'pageIndex',
  'pageSize',
  'sort',
  'isDownload',
];
export const validateQueryParams = (
  queryParams: Record<string, unknown>,
  whitelist: string[] = DEFAULT_QUERY_PARAMS_WHITELIST
) => {
  Object.keys(queryParams).forEach(key => {
    if (!whitelist.includes(key)) {
      throw createError({
        code: 400,
        message: `Invalid query parameter provided: ${key}, valid query parameter is ${whitelist}`,
      });
    }
  });
};
