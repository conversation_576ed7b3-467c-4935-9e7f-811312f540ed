import { applyPagination } from '@ics/core/query/applyPagination';

describe('applyPagination', () => {
  it('should apply provided pagination to sql query', () => {
    const db = {
      offset: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
    };
    // @ts-expect-error
    applyPagination(db, { pageIndex: 2, pageSize: 10 });
    expect(db.offset).toBeCalledWith(20);
    expect(db.limit).toBeCalledWith(10);
  });
});
