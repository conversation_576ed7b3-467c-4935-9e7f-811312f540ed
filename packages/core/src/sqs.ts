import SqsExtendedClient from 'sqs-extended-client';
import {
  SQSClientConfig,
  SQSClient,
  SendMessageCommand,
} from '@aws-sdk/client-sqs';
import { AppConfig } from '@ics/core/types';
import { S3ClientConfig } from '@aws-sdk/client-s3';

class SqsClientSingleton {
  private static instance: SqsExtendedClient | SQSClient | null = null;

  static getInstance(
    appConfig: AppConfig,
    options: {
      sqsClientConfig?: SQSClientConfig;
      s3ClientConfig?: S3ClientConfig;
      messageGroupId?: string;
      extended?: boolean;
    }
  ) {
    if (!this.instance) {
      if (options.extended) {
        this.instance = new SqsExtendedClient({
          bucketName: appConfig.S3.saveRenderedTemplate,
          ...options,
        });
      } else {
        this.instance = new SQSClient(options.sqsClientConfig ?? []);
      }
    }

    return this.instance;
  }
}

export const useSqsClient = (
  appConfig: AppConfig,
  options: {
    sqsClientConfig?: SQSClientConfig;
    s3ClientConfig?: S3ClientConfig;
    messageGroupId?: string;
    extended?: boolean;
  }
) => {
  const sqsClient = SqsClientSingleton.getInstance(appConfig, options);

  return async (queueUrl: string, message: unknown) => {
    if (options.extended) {
      await (sqsClient as SqsExtendedClient).sendMessage({
        QueueUrl: queueUrl,
        MessageBody: JSON.stringify({ detail: message }),
        ...(options.messageGroupId
          ? {
              MessageGroupId: options.messageGroupId,
              MessageDeduplicationId: Date.now().toString(),
            }
          : {}),
      });
    } else {
      const sqs = sqsClient as SQSClient;
      const params = {
        QueueUrl: queueUrl,
        MessageBody: JSON.stringify({ detail: message }),
        ...(options.messageGroupId
          ? {
              MessageGroupId: options.messageGroupId,
              MessageDeduplicationId: Date.now().toString(),
            }
          : {}),
      };

      await sqs.send(new SendMessageCommand(params));
    }
  };
};

export async function publishSQS({
  type,
  message,
  messageGroupId,
  appConfig,
  options: { extended = false } = {},
}: {
  type: keyof AppConfig['SQS'];
  messageGroupId?: string;
  message: unknown;
  appConfig: AppConfig;
  options?: { extended?: boolean };
}) {
  const sqsClient = useSqsClient(appConfig, {
    messageGroupId,
    extended,
  });

  await sqsClient(appConfig.SQS[type], message);
}
