import Redis from 'ioredis';

import { delay } from '../utils/index';
import { getQuorum, smartSum } from '../utils/redlock';
import { Logger } from '@aws-lambda-powertools/logger';

export interface Options {
  identifier: string;
  lockTimeout: number;
  acquireTimeout: number;
  acquireAttemptsLimit: number;
  retryInterval: number;
}

export async function acquireRedlockMutex(
  clients: Redis[],
  key: string,
  options: Options,
  logger: Logger
) {
  const {
    identifier,
    lockTimeout,
    acquireTimeout,
    acquireAttemptsLimit,
    retryInterval,
  } = options;
  let attempt = 0;
  const startTime = Date.now();
  const end = Date.now() + acquireTimeout;
  const quorum = getQuorum(clients.length);
  while (Date.now() < end && ++attempt <= acquireAttemptsLimit) {
    const promises = clients.map(client =>
      client
        .set(key, identifier, 'PX', lockTimeout, 'NX')
        .then(result => (result === 'OK' ? 1 : 0))
        .catch(() => 0)
    );
    const results = await Promise.all(promises);
    if (results.reduce(smartSum, 0) >= quorum) {
      const elapsedTime = Date.now() - startTime;
      logger.info(`Able to acquire the lock in ${elapsedTime} ms`, { elapsedTime, attempt, tenantId: identifier });
      return true;
    }
    await delay(retryInterval);
  }
  return false;
}
