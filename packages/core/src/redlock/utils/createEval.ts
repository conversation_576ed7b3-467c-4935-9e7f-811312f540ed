import { createHash } from 'crypto';
import Redis from 'ioredis';

import { getConnectionName } from './index';

function createSHA1(script: string) {
  return createHash('sha1').update(script, 'utf8').digest('hex');
}

function isNoScriptError(err: Error) {
  return err.toString().indexOf('NOSCRIPT') !== -1;
}

export default function createEval<Args extends Array<number | string>, Result>(
  script: string,
  keysCount: number
) {
  const sha1 = createSHA1(script);
  return async function optimizedEval(
    client: Redis,
    args: Args
  ): Promise<Result> {
    const connectionName = getConnectionName(client);
    const evalSHAArgs = [sha1, keysCount, ...args];
    try {
      return (await client.evalsha(
        sha1,
        keysCount,
        ...args
      )) as Promise<Result>;
    } catch (err: any) {
      if (isNoScriptError(err)) {
        const evalArgs = [script, keysCount, ...args];
        return (await client.eval(
          script,
          keysCount,
          ...args
        )) as Promise<Result>;
      } else {
        throw err;
      }
    }
  };
}
