import * as crypto from 'node:crypto';
import { Logger } from '@aws-lambda-powertools/logger';

import LostLockError from './errors/LostLockError';
import { defaultOnLockLost, defaultTimeoutOptions } from './misc';
import { AcquireOptions, LockLostCallback, LockOptions } from './types';

const REFRESH_INTERVAL_COEF = 0.8;

export abstract class Lock {
  protected abstract _kind: string;
  protected abstract _key: string;
  protected _identifier: string;
  protected _acquireOptions: AcquireOptions;
  protected _refreshTimeInterval: number;
  protected _refreshInterval?: ReturnType<typeof setInterval>;
  protected _refreshing = false;
  protected _acquired = false;
  protected _acquiredExternally = false;
  protected _onLockLost: LockLostCallback;

  protected abstract _refresh(): Promise<boolean>;
  protected abstract _acquire({ logger }: { logger: Logger }): Promise<boolean>;
  protected abstract _release(): Promise<void>;
  protected abstract _update_key(identifier: string): void;

  constructor({
    lockTimeout = defaultTimeoutOptions.lockTimeout,
    acquireTimeout = defaultTimeoutOptions.acquireTimeout,
    acquireAttemptsLimit = defaultTimeoutOptions.acquireAttemptsLimit,
    retryInterval = defaultTimeoutOptions.retryInterval,
    refreshInterval = Math.round(lockTimeout * REFRESH_INTERVAL_COEF),
    onLockLost = defaultOnLockLost,
    externallyAcquiredIdentifier,
    identifierSuffix,
    identifier,
    acquiredExternally,
  }: LockOptions = defaultTimeoutOptions) {
    if (
      identifier !== undefined &&
      (!identifier || typeof identifier !== 'string')
    ) {
      throw new Error('identifier must be not empty random string');
    }
    if (acquiredExternally && !identifier) {
      throw new Error(
        'acquiredExternally=true meanless without custom identifier'
      );
    }
    if (externallyAcquiredIdentifier && (identifier || acquiredExternally)) {
      throw new Error(
        'Invalid usage. Use custom identifier and acquiredExternally: true'
      );
    }
    this._identifier =
      identifier ||
      externallyAcquiredIdentifier ||
      this.getIdentifier(identifierSuffix);
    this._acquiredExternally =
      !!acquiredExternally || !!externallyAcquiredIdentifier;
    this._acquireOptions = {
      lockTimeout,
      acquireTimeout,
      acquireAttemptsLimit,
      retryInterval,
      identifier: this._identifier,
    };
    this._refreshTimeInterval = refreshInterval;
    this._processRefresh = this._processRefresh.bind(this);
    this._onLockLost = onLockLost;
  }

  get identifier() {
    return this._identifier;
  }

  set identifier(identifier: string) {
    this._identifier = identifier;
    this._update_key(identifier);
    this._acquireOptions.identifier = identifier;
  }

  get isAcquired() {
    return this._acquired;
  }

  private getIdentifier(identifierSuffix: string | undefined): string {
    const uuid = crypto.randomUUID();
    return identifierSuffix ? `${uuid}-${identifierSuffix}` : uuid;
  }

  private _startRefresh() {
    this._refreshInterval = setInterval(
      this._processRefresh,
      this._refreshTimeInterval
    );
    this._refreshInterval.unref();
  }

  stopRefresh() {
    if (this._refreshInterval) {
      clearInterval(this._refreshInterval);
    }
  }

  private async _processRefresh() {
    if (this._refreshing) {
      return;
    }
    this._refreshing = true;
    try {
      const refreshed = await this._refresh();
      if (!refreshed) {
        if (!this._acquired) {
          return;
        }
        this._acquired = false;
        this.stopRefresh();
        const lockLostError = new LostLockError(
          `Lost ${this._kind} for key ${this._key}`
        );
        this._onLockLost(lockLostError);
      }
    } finally {
      this._refreshing = false;
    }
  }

  async acquire({ logger }: { logger: Logger }) {
    const acquired = await this.tryAcquire({ logger });
    if (!acquired) {
      this._onLockLost(
        new LostLockError(
          `Failed to acquire ${this._kind} ${this._key} timeout`
        )
      );
    }
  }

  async tryAcquire({ logger }: { logger: Logger }) {
    const acquired = this._acquiredExternally
      ? await this._refresh()
      : await this._acquire({ logger });
    if (!acquired) {
      return false;
    }
    this._acquired = true;
    this._acquiredExternally = false;
    if (this._refreshTimeInterval > 0) {
      this._startRefresh();
    }
    return true;
  }

  async release({ logger }: { logger: Logger }) {
    if (this._refreshTimeInterval > 0) {
      logger.info(`[RedlockMutex] Stopping refresh for key: ${this._key}`, {
        tenantId: this._identifier,
      });
      this.stopRefresh();
    }

    logger.info(
      `[RedlockMutex] Was acquired: ${this._acquired}, or externally: ${this._acquiredExternally}`,
      { tenantId: this._identifier }
    );

    if (!this._acquired && !this._acquiredExternally) {
      this._onLockLost(
        new LostLockError(
          `Lock not acquired, unable to release for key: ${this._key}`
        )
      );
      return;
    }

    await this._release();
    logger.info(`[RedlockMutex] Lock released for key: ${this._key}`, {
      tenantId: this._identifier,
    });

    this._acquired = false;
  }
}
