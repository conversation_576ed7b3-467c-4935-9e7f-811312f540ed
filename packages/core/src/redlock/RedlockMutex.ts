import Redis, { Cluster } from 'ioredis';
import { Logger } from '@aws-lambda-powertools/logger';

import { Lock } from './Lock';
import { defaultTimeoutOptions } from './misc';
import { acquireRedlockMutex } from './redlockMutex/acquire';
import { refreshRedlockMutex } from './redlockMutex/refresh';
import { releaseRedlockMutex } from './redlockMutex/release';
import { LockOptions } from './types';

export default class RedlockMutex extends Lock {
  protected _kind = 'redlock-mutex';
  protected _key: string;
  protected _clients: Redis[] | Cluster[];

  constructor(
    clients: Redis[] | Cluster[],
    key: string,
    options: LockOptions = defaultTimeoutOptions
  ) {
    super(options);
    if (!clients || !Array.isArray(clients)) {
      throw new Error('"clients" array is required');
    }
    if (
      !clients.every(
        client => client instanceof Redis || client instanceof Cluster
      )
    ) {
      throw new Error('"client" must be instance of ioredis client or cluster');
    }
    if (!key) {
      throw new Error('"key" is required');
    }
    if (typeof key !== 'string') {
      throw new Error('"key" must be a string');
    }
    this._clients = clients;
    this._key = `mutex:${key}`;
  }

  protected async _refresh() {
    return await refreshRedlockMutex(
      //@ts-ignore
      this._clients,
      this._key,
      this._identifier,
      this._acquireOptions.lockTimeout
    );
  }

  protected async _acquire({ logger }: { logger: Logger }) {
    return await acquireRedlockMutex(
      //@ts-ignore
      this._clients,
      this._key,
      this._acquireOptions,
      logger
    );
  }

  protected _update_key(identifier: string) {
    this._key = `mutex:${identifier}`;
  }

  protected async _release() {
    //@ts-ignore
    await releaseRedlockMutex(this._clients, this._key, this._identifier);
  }
}
