import Redis from 'ioredis';

import { delay } from '../utils/index';

export interface Options {
  identifier: string;
  lockTimeout: number;
  acquireTimeout: number;
  acquireAttemptsLimit: number;
  retryInterval: number;
}

export async function acquireMutex(
  client: Redis,
  key: string,
  options: Options
) {
  const {
    identifier,
    lockTimeout,
    acquireTimeout,
    acquireAttemptsLimit,
    retryInterval,
  } = options;
  let attempt = 0;
  const end = Date.now() + acquireTimeout;
  while (Date.now() < end && ++attempt <= acquireAttemptsLimit) {
    const result = await client.set(key, identifier, 'PX', lockTimeout, 'NX');
    if (result === 'OK') {
      return true;
    } else {
      await delay(retryInterval);
    }
  }
  return false;
}
