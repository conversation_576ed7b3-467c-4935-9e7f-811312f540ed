export type ResponseMetadata = {
  totalResults: string | number;
  pageIndex: string | number;
  pageSize: string | number;
};

export type ResponseResults = unknown[];
export const toRestResponse = ({
  metadata: { totalResults, pageSize, pageIndex },
  results,
}: {
  metadata: ResponseMetadata;
  results?: ResponseResults;
}) => ({
  statusCode: 200,
  body: {
    resultsMetadata: {
      totalResults: Number(totalResults),
      pageIndex: Number(pageIndex),
      pageSize: Number(pageSize),
    },
    results,
  },
});
