import { CamelCase<PERSON>lug<PERSON>, <PERSON><PERSON><PERSON>, PostgresDialect } from 'kysely';
import { Pool } from 'pg';

import type { AppConfig, Database, AssetDatabase } from './types';

// Table options type
type TableOptions = {
  tableName?: string;
  databaseName?: DatabaseName;
};

// Database configuration type
type DatabaseConfig = {
  name: string;
  host: string;
  userName: string;
  password: string;
};

// Available database names
export type DatabaseName = 'ICSDB' | 'AssetManagement';

// Database schema mapping
type DatabaseSchemaMap = {
  ICSDB: Database;
  AssetManagement: AssetDatabase;
};

// Database instances cache
const dbInstances: Map<DatabaseName, Kysely<any> | null> = new Map();

/**
 * Creates a database instance for a specific database with proper typing
 * @param databaseName - The name of the database (ICSDB or AssetManagement)
 * @returns Function that creates/returns database instance
 */
export const createDbInstance = <T extends DatabaseName>(databaseName: T = 'ICSDB' as T) => {
  return (config: AppConfig): Kysely<DatabaseSchemaMap[T]> => {
    let instance = dbInstances.get(databaseName);

    if (!instance) {
      const dbConfig = config[databaseName] as DatabaseConfig;

      if (!dbConfig) {
        throw new Error(`Database configuration for '${databaseName}' not found in AppConfig`);
      }

      // Determine SSL configuration based on environment
      const isLocal = dbConfig.host === 'localhost' ||
        dbConfig.host === '127.0.0.1' ||
        dbConfig.host.includes('localhost');

      const sslConfig = isLocal ? false : { rejectUnauthorized: false };

      instance = new Kysely<DatabaseSchemaMap[T]>({
        dialect: new PostgresDialect({
          pool: new Pool({
            host: dbConfig.host,
            database: dbConfig.name,
            user: dbConfig.userName,
            password: dbConfig.password,
            ssl: sslConfig,
            connectionTimeoutMillis: 10000,
            max: 1,
            idleTimeoutMillis: 0,
          }),
        }),
        plugins: [new CamelCasePlugin()],
      });

      // Cache the instance
      dbInstances.set(databaseName, instance);
    }
    return instance as Kysely<DatabaseSchemaMap[T]>;
  };
};

// Default database instance (ICSDB for backward compatibility)
export const useDB = createDbInstance('ICSDB');

// Asset Management database instance
export const useAssetDB = createDbInstance('AssetManagement');

/**
 * Enhanced useTable function with database selection support
 * @param appConfig - Application configuration
 * @param options - Table and database options
 */
export function useTable(
  appConfig: AppConfig,
  { tableName = 'certificateReferences', databaseName = 'ICSDB' }: TableOptions
) {
  if (databaseName === 'ICSDB') {
    const db = useDB(appConfig);
    const selectFromTable = tableName ? db.selectFrom(tableName as keyof Database) : null;
    return { db, selectFromTable };
  } else if (databaseName === 'AssetManagement') {
    const db = useAssetDB(appConfig);
    const selectFromTable = tableName ? db.selectFrom(tableName as keyof AssetDatabase) : null;
    return { db, selectFromTable };
  }

  throw new Error(`Unsupported database: ${databaseName}`);
}

/**
 * Get a specific database instance
 * @param appConfig - Application configuration
 * @param databaseName - Name of the database to connect to
 */
export function useDatabase(appConfig: AppConfig, databaseName: DatabaseName) {
  if (databaseName === 'ICSDB') {
    return useDB(appConfig);
  } else if (databaseName === 'AssetManagement') {
    return useAssetDB(appConfig);
  }

  throw new Error(`Unsupported database: ${databaseName}`);
}

/**
 * Utility function to get ICSDB instance (backward compatibility)
 * @param appConfig - Application configuration
 */
export function useICSDB(appConfig: AppConfig) {
  return useDB(appConfig);
}

/**
 * Utility function to get AssetManagement database instance
 * @param appConfig - Application configuration
 */
export function useAssetManagementDB(appConfig: AppConfig) {
  return useAssetDB(appConfig);
}

/**
 * Close all database connections
 */
export async function closeAllDatabases() {
  const promises: Promise<void>[] = [];

  for (const [databaseName, instance] of dbInstances.entries()) {
    if (instance) {
      promises.push(instance.destroy());
      dbInstances.set(databaseName, null);
    }
  }

  await Promise.all(promises);
}
