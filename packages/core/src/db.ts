import { CamelCasePlugin, Kysely, PostgresDialect } from 'kysely';
import { Pool } from 'pg';

import type { AppConfig, Database } from './types';

type Options = {
  tableName?: keyof Database;
};

export const createDbInstance = () => {
  let instance: Kysely<Database> | null = null;

  return (config: AppConfig) => {
    if (!instance) {
      // Determine SSL configuration based on environment
      const isLocal = config.ICSDB.host === 'localhost' ||
        config.ICSDB.host === '127.0.0.1' ||
        config.ICSDB.host.includes('localhost');

      const sslConfig = isLocal ? false : { rejectUnauthorized: false };

      instance = new Kysely<Database>({
        dialect: new PostgresDialect({
          pool: new Pool({
            host: config.ICSDB.host,
            database: config.ICSDB.name,
            user: config.ICSDB.userName,
            password: config.ICSDB.password,
            ssl: sslConfig,
            connectionTimeoutMillis: 10000,
            max: 1,
            idleTimeoutMillis: 0,
          }),
        }),
        plugins: [new CamelCasePlugin()],
      });
    }
    return instance;
  };
};

export const useDB = createDbInstance();

export function useTable(
  appConfig: AppConfig,
  { tableName = 'certificate_email_notification' }: Options
) {
  const db = useDB(appConfig);

  const selectFromTable = db.selectFrom(tableName);

  return { db, selectFromTable };
}
