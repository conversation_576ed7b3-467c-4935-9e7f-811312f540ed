import { toRestResponse } from '@ics/core/toRestResponse';

describe('toRestResponse', () => {
  it('should return response formatted to API standards', () => {
    const response = toRestResponse({
      metadata: {
        totalResults: '5',
        pageSize: '5',
        pageIndex: '0',
      },
      results: [],
    });

    expect(response).toStrictEqual({
      statusCode: 200,
      body: {
        resultsMetadata: { totalResults: 5, pageSize: 5, pageIndex: 0 },
        results: [],
      },
    });
  });
});
