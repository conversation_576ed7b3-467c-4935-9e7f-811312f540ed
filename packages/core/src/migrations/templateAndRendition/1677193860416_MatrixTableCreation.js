/* eslint-disable no-unused-vars */
import { Kysely, sql } from 'kysely';

/**
 * @param db {Kysely<any>}
 */
export async function up(db) {
  await db.schema.createSchema('config_matrix').execute();

  await db.schema
    .withSchema('config_matrix')
    .createTable('config_file_device_status')
    .addColumn('config_file_device_id', 'bigserial', col => col.notNull())
    .addColumn('device_id', 'varchar(50)', col => col.notNull())
    .addColumn('config_file_id', 'bigint', col => col.notNull())
    .addColumn('application_name', 'varchar(50)', col => col.notNull())
    .addColumn('tenant_id', 'varchar(50)', col => col.notNull())
    .addColumn('site_id', 'varchar(50)', col => col.notNull())
    .addColumn('site_tag_id', sql`bigint[]`)
    .addColumn('variables_used', 'jsonb')
    .addColumn('tenant_config_file_instance_id', 'bigint')
    .addColumn('site_tag_config_file_instance_id', 'bigint')
    .addColumn('site_config_file_instance_id', 'bigint')
    .addColumn('device_config_file_instance_id', 'bigint')
    .addColumn('desired_config_file_instance_id', 'bigint')
    .addColumn('desired_rendition_hash', 'varchar(64)')
    .addColumn('deployed_config_file_instance_id', 'bigint')
    .addColumn('deployed_config_revision_id', 'bigint')
    .addColumn('deployed_rendition_hash', 'varchar(64)')
    .addColumn('is_deploying', 'int4')
    .addColumn('last_deploy_at', 'timestamptz')
    .addColumn('is_new_version_available', 'int4')
    .addColumn('is_matching', 'int4')
    .addColumn('device_cfact_hash', 'varchar(64)')
    .addColumn('device_cfloc_hash', 'varchar(64)')
    .addColumn('device_cfrem_hash', 'varchar(64)')
    .addColumn('last_device_report_at', 'timestamptz')
    .addPrimaryKeyConstraint('PK_MatrixId', ['config_file_device_id'])
    .addUniqueConstraint('UQ_DeviceId_ConfigFileId', [
      'device_id',
      'config_file_id',
    ])
    .execute();

  await db.schema
    .withSchema('config_matrix')
    .createIndex('ids_unique_index')
    .on('matrix_device_file')
    .columns(['device_id', 'site_id', 'tenant_id'])
    .execute();
}

/**
 * @param db {Kysely<any>}
 */
export async function down(db) {
  await db.schema
    .withSchema('config_matrix')
    .dropIndex('ids_unique_index')
    .execute();

  await db.schema
    .withSchema('config_matrix')
    .dropTable('matrix_device_file')
    .execute();

  await db.schema.dropSchema('config_matrix').execute();
}
