import { Kafka, KafkaConfig, Partitioners } from 'kafkajs';
import { generateAuthToken } from 'aws-msk-iam-sasl-signer-js';
import { AppConfig } from '@ics/core/types';
import { CustomKafkaConfig, IKafkaPayload } from '@ics/core/types/kafka';
import { Logger } from '@aws-lambda-powertools/logger';

async function oauthBearerTokenProvider(region: string) {
    const authTokenResponse = await generateAuthToken({ region });
    return {
        value: authTokenResponse.token,
    };
}

async function initKafka(client: string, kafkaConfig: CustomKafkaConfig) {
    const {
        KAFKA_BROKER_URLS,
        KAFKA_CLIENT_ID,
        KAFKA_CONSUMER_CONFIG,
        KAFKA_CONNECTION_TIMEOUT_MS,
    } = kafkaConfig;
    const clientId = client ?? KAFKA_CLIENT_ID;
    const brokers = KAFKA_BROKER_URLS || ['localhost:9092'];
    const clientKafkaConfig: KafkaConfig = {
        clientId,
        brokers,
        connectionTimeout: KAFKA_CONNECTION_TIMEOUT_MS ?? 3000,
        retry: {
            initialRetryTime: KAFKA_CONSUMER_CONFIG?.retry?.initialRetryTime ?? 300,
            maxRetryTime: KAFKA_CONSUMER_CONFIG?.retry?.maxRetryTime ?? 30000,
            retries: KAFKA_CONSUMER_CONFIG?.retry?.maxRetriesPerCall ?? 15,
            factor: KAFKA_CONSUMER_CONFIG?.retry?.factor ?? 0.2,
            multiplier: KAFKA_CONSUMER_CONFIG?.retry?.multiplier ?? 2,
        },
        ...(kafkaConfig.iamAuth && {
            ssl: true,
            sasl: {
                mechanism: 'oauthbearer',
                oauthBearerProvider: () =>
                    oauthBearerTokenProvider(kafkaConfig.iamAuth?.region ?? 'ap-south-1'),
            },
        }),
    };
    const kafka = new Kafka(clientKafkaConfig);
    return kafka;
}

export const eventPublish = async (
    appConfig: AppConfig,
    payload: IKafkaPayload,
    topic: string,
    logger: Logger
): Promise<void> => {
    const clientId = `${topic}-v1`;
    logger.info(`Kafka client id and topic ->-> ${clientId}`,{payload});
    const mskObj = await initKafka(clientId, appConfig.kafkaConfig);
    if (!mskObj) {
        logger.error('Kafka client is not initialized.');
        throw new Error('Kafka client is not initialized.');
    }
    const producer = mskObj.producer({
        createPartitioner: Partitioners.DefaultPartitioner,
    });
    if (!producer) {
        logger.error('Kafka producer is not initialized.');
        throw new Error('Kafka producer is not initialized.');
    }
    await producer.connect();
    try {
        let messagesToSend: { value: string }[] = [];
        if (Array.isArray(payload)) {
            messagesToSend = payload.map(val => ({
                value: JSON.stringify(val),
            }));
        } else {
            messagesToSend = [{ value: JSON.stringify(payload) }];
        }
        logger.info(`Sending message to audit event processor:`,{messagesToSend});
        const result = await producer.send({
            topic,
            messages: messagesToSend,
        });
        logger.info(`Message sent successfully to audit event processor:`,{result});
    } catch (error) {
        logger.error(`Error sending message to audit event processor:`,{error});
    } finally {
        await producer.disconnect();
    }
};