const updateLatestRecord = async ({
  logger,
  data,
  selectFromTable,
  updateTable,
}: any) => {
  if (data?.isLast) {
    const [latestRecord] = await selectFromTable
      .select(['configFileDeviceStatusId'])
      .where('deviceId', '=', data.deviceId)
      .where('isNewVersionAvailable', '=', true)
      .orderBy('updatedDate', 'desc')
      .orderBy('createdDate', 'desc')
      .limit(1)
      .execute();

    logger.info(
      `[setIsLastOnDeletion] Latest record is ${latestRecord?.configFileDeviceStatusId} for device ${data.deviceId}`
    );

    await updateTable
      .set({ isLast: true })
      .where(
        'configFileDeviceStatusId',
        '=',
        latestRecord?.configFileDeviceStatusId!
      )
      .executeTakeFirst();
  }
};

export const setIsLastOnDeletion = async (dbData: any) => {
  const { data, logger } = dbData;

  if (!data) {
    return;
  }
  if (Array.isArray(data) && data.length !== 0) {
    const promises = data.map(row => updateLatestRecord(row));

    const results = await Promise.allSettled(promises);

    results.forEach((result: any) => {
      if (result.status === 'rejected') {
        logger.info(
          '[setIsLastOnDeletion] Error on database action',
          result.reason
        );
      }
    });
  } else {
    await updateLatestRecord(dbData);
  }
};
