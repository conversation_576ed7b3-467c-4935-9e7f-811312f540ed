import { Kysely, sql } from 'kysely';

import { CfgList, Database, RestartData } from '../types';

type GetRestartAppDataFromAppDescriptor = {
  db: Kysely<Database>;
  appNames: (string | null)[];
  tenantId: string;
};

export const getRestartAppDataFromAppDescriptor = async ({ db, appNames, tenantId }: GetRestartAppDataFromAppDescriptor): Promise<RestartData[]> => db
.selectFrom('config.appDescriptor as ad')
.select([
  sql<string[]>`ad.app_descriptor_json->'restart-data'->'applications'`.as('applications'),
  sql<string>`ad.app_name`.as('appName'),
  sql<CfgList[]>`ad.app_descriptor_json->'cfglist'`.as('cfgList'),
])
.where('ad.company_id', '=', tenantId)
.where('ad.app_name', 'in', appNames)
.whereRef(
  'ad.app_descriptor_id',
  '=',
  db
    .selectFrom('config.appDescriptor')
    .select(sql<number>`MAX(app_descriptor_id)`.as('maxId'))
    .whereRef('app_name', '=', sql`ad.app_name`)
    .where('company_id', '=', tenantId)
)
.execute();


