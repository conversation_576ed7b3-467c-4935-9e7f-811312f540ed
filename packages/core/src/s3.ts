import { GetObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Kysely, sql } from 'kysely';
import { createError } from '@ics/core/createError';
import { Database, ConfigFileContent } from '@ics/core/types/db';

export interface ComparisonFilter<T> {
  $eq?: T;
}

type Filtered<T> = {
  [K in keyof T]: ComparisonFilter<T[K]>;
};

export const createPresignedUrlWithClient = ({
  bucket,
  key,
}: {
  bucket: string;
  key: string;
}) => {
  const client = new S3Client();
  const command = new GetObjectCommand({
    Bucket: bucket,
    Key: key,
    ResponseContentDisposition: 'attachment; filename="deployed-revison.json"',
  });
  return getSignedUrl(client, command, { expiresIn: 3600 });
};

export const downloadContent = async (
  db: Kysely<Database>,
  filters: Filtered<ConfigFileContent>
) => {
  const configFileContentId = filters?.configFileContentId?.$eq;
  const configFileInstanceId = filters?.configFileInstanceId?.$eq;
  const { configContent } = (await db
    .selectFrom('configInstance.configFileContent')
    .select([sql`REPLACE(config_content, 's3://', '')`.as('configContent')])
    .where('configFileContentId', '=', configFileContentId)
    .where('configFileInstanceId', '=', configFileInstanceId)
    .where('configContent', 'is not', null)
    .executeTakeFirstOrThrow(() => {
      throw createError({
        code: 404,
        message: 'File not found!',
      });
    })) as any;

  const [bucketName, ...bucketPaths] = configContent.split('/');
  const bucketPath = bucketPaths.join('/');
  const clientUrl = await createPresignedUrlWithClient({
    bucket: bucketName,
    key: bucketPath,
  });

  return clientUrl;
};
