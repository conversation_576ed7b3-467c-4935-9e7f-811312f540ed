import type { ColumnType } from 'kysely';

export type Uuid = ColumnType<string, string>;
export type Timestamp = ColumnType<Date, Date | string, Date | string>;

export interface CertificateEmailNotification {
  uuid: Uuid;
  count: number;
  send_notification: boolean;
  created_date: Timestamp;
  updated_date: Timestamp | null;
}

export interface CertificateReference {
  id: ColumnType<number, number | undefined, never>;
  target_id: string;
  certificate_expiry: string;
  count: number;
  last_notification_count: number | null;
  last_notification_date: Timestamp | null;
  created_date: Timestamp;
  updated_date: Timestamp | null;
}

export interface CsrRequestDevice {
  csr_request_device_id: Uuid;
  csr_request_session_id: Uuid;
  device_id: number;
  job_id: Uuid | null;
  status: number | null;
  message: string | null;
  date_signing: Timestamp | null;
  context_snapshot: any | null;
  session_id: string | null;
}

export interface CsrRequestSession {
  csr_request_session_id: Uuid;
  company_id: Uuid;
  created: Timestamp;
  authorized_time: Timestamp | null;
  expires_time: Timestamp | null;
  request_user_id: Uuid;
  authorizer_user_id: Uuid | null;
  status: number;
  name: string | null;
  second_authorized_time: Timestamp | null;
  second_authorizer_user_id: Uuid | null;
  csr_type: string | null;
}

export interface User {
  id: Uuid;
  email: string;
  full_name: string;
  password_hash: string;
  status: number;
  company_id: Uuid;
  mfa_secret: string | null;
  created: Timestamp;
  email_verified: boolean;
  type: string;
  failed_login_attempts: number;
  last_locked: Timestamp | null;
  persona_id: string | null;
}

export interface Database {
  certificate_email_notification: CertificateEmailNotification;
  certificate_references: CertificateReference;
  csr_request_device: CsrRequestDevice;
  csr_request_session: CsrRequestSession;
  user: User;
}
