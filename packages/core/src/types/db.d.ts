import type { ColumnType } from 'kysely';

export type Uuid = ColumnType<string, string>;
export type Timestamp = ColumnType<Date, Date | string, Date | string>;

export interface CertificateEmailNotification {
  uuid: Uuid;
  count: number;
  sendNotification: boolean;
  createdDate: Timestamp;
  updatedDate: Timestamp | null;
}

export interface CertificateReference {
  id: ColumnType<number, number | undefined, never>;
  targetId: string;
  certificateExpiry: string;
  count: number;
  lastNotificationCount: number | null;
  lastNotificationDate: Timestamp | null;
  createdDate: Timestamp;
  updatedDate: Timestamp | null;
}

export interface CsrRequestDevice {
  csrRequestDeviceId: Uuid;
  csrRequestSessionId: Uuid;
  deviceId: number;
  jobId: Uuid | null;
  status: number | null;
  message: string | null;
  dateSigning: Timestamp | null;
  contextSnapshot: any | null;
  sessionId: string | null;
}

export interface CsrRequestSession {
  csrRequestSessionId: Uuid;
  companyId: Uuid;
  created: Timestamp;
  authorizedTime: Timestamp | null;
  expiresTime: Timestamp | null;
  requestUserId: Uuid;
  authorizerUserId: Uuid | null;
  status: number;
  name: string | null;
  secondAuthorizedTime: Timestamp | null;
  secondAuthorizerUserId: Uuid | null;
  csrType: string | null;
}

export interface User {
  id: Uuid;
  email: string;
  fullName: string;
  passwordHash: string;
  status: number;
  companyId: Uuid;
  mfaSecret: string | null;
  created: Timestamp;
  emailVerified: boolean;
  type: string;
  failedLoginAttempts: number;
  lastLocked: Timestamp | null;
  personaId: string | null;
}

export interface Database {
  certificateEmailNotification: CertificateEmailNotification;
  certificateReferences: CertificateReference;
  csrRequestDevice: CsrRequestDevice;
  csrRequestSession: CsrRequestSession;
  user: User;
}
