export type CustomKafkaConfig = {
    KAFKA_BROKER_URLS: string[];
    KAFKA_CLIENT_ID?: string;
    KAFKA_CONSUMER_GROUP: string;
    KAFKA_CONNECTION_TIMEOUT_MS?: number;
    KAFKA_CONSUMER_CONFIG: {
        partitionsConsumedConcurrently?: number;
        minBytes: number;
        maxBytes: number;
        sessionTimeoutMS: number;
        heartBeatIntervalMS: number;
        retry?: {
            initialRetryTime?: number;
            maxRetryTime?: number;
            maxRetriesPerCall?: number;
            factor?: number;
            multiplier?: number;
        };
    };
    publisherId: string;
    iamAuth?: {
        region: string;
    };
    topics?: {
        eventProcessorService: string;
        panicService: string;
        eventDetailService: string;
    };
};

type Record<K extends keyof any, T> = {
    [P in K]: T;
};
export type IKafkaPayload = Record<string, unknown>[] | Record<string, unknown>;