import type { ColumnType } from 'kysely';

export type Uuid = ColumnType<string, string>;
export type Timestamp = ColumnType<Date, Date | string, Date | string>;

// Asset Management database tables
export interface CsrRequestDevice {
  csrRequestDeviceId: Uuid;
  csrRequestSessionId: Uuid;
  deviceId: number;
  jobId: Uuid | null;
  status: number | null;
  message: string | null;
  dateSigning: Timestamp | null;
  contextSnapshot: any | null;
  sessionId: string | null;
}

export interface CsrRequestSession {
  csrRequestSessionId: Uuid;
  companyId: Uuid;
  created: Timestamp;
  authorizedTime: Timestamp | null;
  expiresTime: Timestamp | null;
  requestUserId: Uuid;
  authorizerUserId: Uuid | null;
  status: number;
  name: string | null;
  secondAuthorizedTime: Timestamp | null;
  secondAuthorizerUserId: Uuid | null;
  csrType: string | null;
}

// Asset Management Database interface
export interface AssetDatabase {
  csrRequestDevice: CsrRequestDevice;
  csrRequestSession: CsrRequestSession;
}
