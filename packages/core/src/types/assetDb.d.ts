import type { ColumnType } from 'kysely';

export type Uuid = ColumnType<string, string>;
export type Timestamp = ColumnType<Date, Date | string, Date | string>;

// Asset Management database tables
export interface Asset {
  id: ColumnType<number, number | undefined, never>;
  assetId: string;
  assetName: string;
  assetType: string;
  status: string;
  location: string | null;
  createdDate: Timestamp;
  updatedDate: Timestamp | null;
}

export interface AssetCategory {
  id: ColumnType<number, number | undefined, never>;
  categoryName: string;
  description: string | null;
  createdDate: Timestamp;
  updatedDate: Timestamp | null;
}

export interface AssetLocation {
  id: ColumnType<number, number | undefined, never>;
  locationName: string;
  address: string | null;
  coordinates: string | null;
  createdDate: Timestamp;
  updatedDate: Timestamp | null;
}

// Asset Management Database interface
export interface AssetDatabase {
  asset: Asset;
  assetCategory: AssetCategory;
  assetLocation: AssetLocation;
}
