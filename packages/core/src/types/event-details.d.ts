import { ConfigFileDeviceStatusFile, Int8, RestartData } from './db';

export type DeploymentType = 'maintenance-window' | 'immediate';

export type RenditionEventBase = {
  tenantId: string;
  deploymentType: DeploymentType;
};

export type CalculateDeviceStatus = RenditionEventBase & {
  calculateDesiredConfigInstance?: boolean;
  configFileDeviceIds: string[];
  renderTemplateInvalidateCache?: boolean;
  deployDisabledSites?: boolean;
  auditId?: number;
};

export type CreateDevice = RenditionEventBase & {
  deviceId: string;
  siteId: string;
  updatedBy: string;
  updatedDate: string;
  configFileIdsExist?: string[];
};

export type DeleteDevice = RenditionEventBase & {
  deviceId: string;
  deletedDate: string;
  updatedBy: string;
  auditId: number;
};

export type DeploymentTriggered = RenditionEventBase & {
  entities: {
    deviceIds: number[];
    configFileId: string;
    deployedConfigFileInstanceId: string;
    deployedRenditionHash: string;
    revisionId: string;
    jobId?: string;
    jobStatus?: string;
    type: 'sys.restart' | 'sys.reboot' | 'sys.configfile-update';
    configFileDeviceStatusId: string;
    deploymentType: string;
  }[];
  auditId: number;
};

export type UpdateCustomVariables = RenditionEventBase & {
  attributes: {
    attributeDefinitionId: number;
    entityId: string;
  }[];
  updatedBy: string;
  updatedDate: string;
  auditId: number;
};

export type DeploymentUpdateStatus = RenditionEventBase & {
  deviceId: string;
  status: number;
  deployedJobId: string;
  type: string;
  jobId: string;
  jobType?: string;
};

export type PublishNewRevision = RenditionEventBase & {
  revisionId: string;
  configFileInstanceId: string;
  updatedBy: string;
  updatedDate: string;
  auditId: number;
};

export type MoveDeviceToSite = RenditionEventBase & {
  deviceId: string;
  siteId: string;
  updatedBy: string;
  updatedDate: string;
  auditId: number;
};

export type RemoveSiteTags = RenditionEventBase & {
  siteId: string;
  siteTagId: Int8;
  updatedBy: string;
  updatedDate: string;
  auditId: number;
};

export type AddSiteTag = RenditionEventBase & {
  siteId: string;
  siteTagId: Int8;
  updatedBy: string;
  updatedDate: string;
  auditId: number;
};

export type Assignments= {
  tenantIds: string[];
  siteIds: string[];
  siteTagIds: string[];   
  deviceIds: string[];
}

export type UpdateConfigInstanceAssignment = {
  configInstanceId: string;
  configFileId: string;
  oldAssignments: Assignments;
  newAssignments: Assignments;
  updatedBy: string;
  updatedDate: string;
  deploymentType: string;
  appName: string;
  auditId: number;
};

export type UpdateConfigInstanceAssignments = RenditionEventBase &
  UpdateConfigInstanceAssignment;

export type UpdateSiteGroup = {
  tenantId: string;
  siteGroupId: string;
  type: 'create' | 'update' | 'delete';
};

export type RenderTemplate = RenditionEventBase & {
  siteId: string;
  template?: string;
  renderTemplateInvalidateCache?: boolean;
  configFileDeviceId: string;
  configInstanceId: string;
  configFileId: string;
  shouldUpdateHash: boolean;
  deviceId: string;
  createdBy: string | null;
  updatedBy?: string | null;
  deployedRenditionHash?: string | null;
  appName: string | null | string[];
  disableAutomaticDeployment: boolean | false;
  activationMethod?: 'restart' | 'reboot' | 'none';
  restartData?: Omit<RestartData, 'cfgList'>;
  auditId?: number;
};

export type UpdateDesiredRenditionHash = RenditionEventBase & {
  hash: string;
  configFileDeviceId: string;
  error: boolean;
  deploymentType: string;
  deviceId: string;
  auditId?: number;
  updatedBy?: string;
};

export type CalculateDeploymentType = RenditionEventBase & {
  configFileDeviceId: string;
};

export type ConfigListReceived = RenditionEventBase & {
  deviceId: string;
  updatedDate: string;
  configList: {
    //  h:hash t:type n:name
    h: string;
    t: string;
    n?: string;
  }[];
};

export type DeviceHashReported = RenditionEventBase & {
  deviceId: string;
  updatedDate: string;
  updatedBy?: string;
  configList: {
    //  h:hash t:type n:name
    h: string;
    t: string;
    n?: string;
  }[];
};

export type Row = Omit<ConfigFileDeviceStatusFile, 'configFileDeviceStatusId'>;

export type InsertRow = RenditionEventBase & {
  tenantId: string;
  deploymentType: DeploymentType;
  rows: Row[];
  releaseTenantLock?: boolean;
  auditId?: number;
};

export type UpdatedAssignmentDevice = {
  id: number;
  name: string;
  serialNumber: string;
  deviceType: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  site: {
    id: string;
    tags: {
      id: number;
    }[];
    visible: boolean;
  };
  company: {
    id: string;
  };
};

export type UpdatedAssignmentDevices = {
  resultsMetadata: {
    totalResults: number;
    pageIndex: number;
    pageSize: number;
  };
  results: UpdatedAssignmentDevice[];
};

export type BuildRow = {
  device: UpdatedAssignmentDevice;
} & UpdateConfigInstanceAssignment;

export type PaginationRequestParams = {
  filters?: any;
  pageIndex?: number;
  pageSize?: number;
  isForSiteTagIds?: boolean;
};

export type BuildRowResult = {
  deviceId: number;
  configFileId: string;
  tenantId: string;
  siteId: string;
  siteTagId: string[] | null;
  tenantConfigFileInstanceId: string | null;
  siteTagConfigFileInstanceId: string | null;
  siteConfigFileInstanceId: string | null;
  deviceConfigFileInstanceId: string | null;
  createdDate: Date | string;
  createdBy: string;
  deploymentType: string;
  appName: string;
};

export type UpdateAssignmentRows = {
  tenantId: string;
  deploymentType: DeploymentType;
  rows: BuildRowResult[];
  releaseTenantLock?: boolean;
  auditId?:number;
};
