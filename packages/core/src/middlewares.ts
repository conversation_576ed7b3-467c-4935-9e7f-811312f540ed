import middy from '@middy/core';
import doNotWaitForEmptyEventLoop from '@middy/do-not-wait-for-empty-event-loop';
import {
  Metrics,
  MetricUnits,
  logMetrics,
} from '@aws-lambda-powertools/metrics';
import { Tracer, captureLambdaHandler } from '@aws-lambda-powertools/tracer';
import { Logger, injectLambdaContext } from '@aws-lambda-powertools/logger';

import type { <PERSON><PERSON> } from 'aws-lambda';

import { injectAppConfig } from './middleware/injectAppConfig';

const namespace = 'ICS';
const environment = process.env.ENVIRONMENT || 'martini';

const useMiddleware = (
  serviceName: string,
  {
    injectConfig = true,
  } = {}
) => {
  const tracer = new Tracer({ serviceName });
  const metrics = new Metrics({ namespace, serviceName });
  const logger = new Logger({
    serviceName,
    persistentLogAttributes: {
      environment,
      tags: {
        Env: environment,
      },
    },
  });

  const middyWrapper = <TEvent = unknown, TResult = unknown>(
    handler: Handler<TEvent, TResult>
  ) => {
    const middyMiddleware = middy(handler)
      .use(doNotWaitForEmptyEventLoop({ runOnError: true }))
      .use(logMetrics(metrics))
      .use(injectLambdaContext(logger, { logEvent: true }))
      .use(captureLambdaHandler(tracer));

    if (injectConfig) {
      middyMiddleware.use(injectAppConfig());
    }

    return middyMiddleware;
  };

  return {
    middyWrapper,
    tracer,
    metrics,
    logger,
  };
};

export { useMiddleware, MetricUnits };
