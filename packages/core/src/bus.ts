import { EventBus } from 'sst/node/event-bus';
import {
  EventBridgeClient,
  PutEventsCommand,
} from '@aws-sdk/client-eventbridge';
import { chunk } from 'lodash';
import { reportAuditError } from 'packages/functions/renditions/src/utils';

import type {
  NormalizedSQSEvent,
  CustomSQSHandler,
  CustomSQSEvent,
  DeleteDevice,
  CreateDevice,
  UpdateCustomVariables,
  DeploymentTriggered,
  DeploymentUpdateStatus,
  PublishNewRevision,
  RemoveSiteTags,
  MoveDeviceToSite,
  AddSiteTag,
  RenderTemplate,
  UpdateDesiredRenditionHash,
  DeviceHashReported,
  AppConfig,
  BuildRowResult,
  UpdateAssignmentRows,
} from './types';

import {
  CalculateDeploymentType,
  CalculateDeviceStatus,
  CustomRecords,
  InsertRow,
} from './types';

export interface Events {
  rendition_calculate_device_status: CalculateDeviceStatus;
  rendition_delete_device: DeleteDevice;
  rendition_create_device: CreateDevice;
  rendition_update_custom_variables: UpdateCustomVariables;
  rendition_deployment_triggered: DeploymentTriggered;
  rendition_deployment_update_status: DeploymentUpdateStatus;
  rendition_publish_new_revision: PublishNewRevision;
  rendition_move_device_to_site: MoveDeviceToSite;
  rendition_remove_site_tag: RemoveSiteTags;
  rendition_add_site_tag: AddSiteTag;
  rendition_render_template: RenderTemplate;
  rendition_update_desired_rendition_hash: UpdateDesiredRenditionHash;
  rendition_calculate_deployment_type: CalculateDeploymentType;
  rendition_insert_row: InsertRow | UpdateAssignmentRows;
  device_hash_reported: DeviceHashReported;
}

type EventTypes = keyof Events;

const client = new EventBridgeClient({});

type LocalEventBusName = 'cmBus' | 'default';

export async function publish<Type extends EventTypes>({
  type,
  props,
  eventBusName,
  localEventBusName,
}: {
  type: Type;
  props: Events[Type];
  eventBusName: string;
  localEventBusName?: LocalEventBusName;
}) {
  function getEventBusName() {
    if (!process.env.IS_LOCAL) {
      return eventBusName;
    }

    const eventBuses: {
      [key in LocalEventBusName]: string;
    } = {
      cmBus: EventBus.cmBus.eventBusName,
      default: EventBus.renditionsEventBus.eventBusName,
    };

    return localEventBusName
      ? eventBuses[localEventBusName]
      : eventBuses.default;
  }

  await client.send(
    new PutEventsCommand({
      Entries: [
        {
          EventBusName: getEventBusName(),
          Source: 'ICS',
          Detail: JSON.stringify(props),
          DetailType: type,
        },
      ],
    })
  );
}

function normalizeEvent<Details>(
  record: CustomRecords<Details>,
  event: CustomSQSEvent<Details>
): NormalizedSQSEvent<Details> {
  const {
    body: { detail, ...bodyRest },
    ...recordRest
  } = record;

  return {
    detail,
    meta: {
      ...recordRest,
      ...bodyRest,
      axiosInstance: event.axiosInstance,
    },
    appConfig: event.appConfig,
  };
}

export function subscribe<Detail, ReturnData = void>(
  handler: CustomSQSHandler<NormalizedSQSEvent<Detail>, ReturnData>
): CustomSQSHandler<CustomSQSEvent<Detail>, ReturnData> {
  // eslint-disable-next-line consistent-return
  return async (event, context, callback) => {
    // To allow function to invoke manually and get response
    if (
      event.Records.length === 1 &&
      event.Records[0].eventSource !== 'aws:sqs'
    ) {
      return handler(
        normalizeEvent(event.Records[0], event),
        context,
        callback
      );
    }
    const handlerPromises = event.Records.map(record =>
      handler(normalizeEvent(record, event), context, callback)
    );


    const results = await Promise.allSettled(handlerPromises);

    // Accumulate errors
    const auditErrors: { auditId: number; error: { message: string } }[] = [];

    const { axiosInstance } = event;

    results.forEach((result, index) => {
      const record = event.Records[index];
      const { detail: { auditId } } = record.body as { detail: { auditId: number } };
      if (result.status === 'fulfilled') {
        console.log(`Audit ID ${auditId} completed successfully`);
        return; 
      } 
        if (auditId) {
          const errorMessage: [string, string] =
            result.reason instanceof Error
              ? [result.reason.name, result.reason.message]
              : ['Unknown error', 'Unknown error'];

          auditErrors.push({
            auditId,
            error: { message: `${errorMessage[0]}: ${errorMessage[1]}` },
          });
        }
    });

    if (auditErrors.length > 0) {
      try {
        await reportAuditError({
          audits: auditErrors,
          axiosInstance,
        });
      } catch (error) {
        console.error(`Failed to report audit error`, error);
      }
    }
  }
}

export async function publishToCalculateDeviceStatusInChunks(
  props: CalculateDeviceStatus,
  appConfig: AppConfig
) {
  const { configFileDeviceIds, ...rest } = props;
  const chunkMaps = chunk(configFileDeviceIds, 25);
  const promises = chunkMaps.map(item =>
    publish({
      type: 'rendition_calculate_device_status',
      props: {
        configFileDeviceIds: item as unknown as string[],
        ...rest,
      },
      eventBusName: appConfig.EventBridge.renditions,
    })
  );

  const results = await Promise.allSettled(promises);

  results.forEach(result => {
    if (result.status === 'rejected') {
      console.error(
        `Publish event with type rendition_calculate_device_status failed with ${result.reason} error`
      );
    }
  });
}

export async function publishToInsertRowsInChunks(
  props: UpdateAssignmentRows,
  appConfig: AppConfig
) {
  const { rows, ...rest } = props;
  const chunkMaps = chunk(rows, 100);
  const promises: Promise<any>[] = [];

  const publishChunk = (chunkRows: BuildRowResult[], isLast = false) => {
    promises.push(
      publish({
        type: 'rendition_insert_row',
        props: {
          rows: chunkRows,
          ...rest,
          releaseTenantLock: isLast,
        },
        eventBusName: appConfig.EventBridge.renditions,
      })
    );
  };

  chunkMaps.forEach((chunkRows, index) => {
    const isLastChunk = index === chunkMaps.length - 1;
    publishChunk(chunkRows as BuildRowResult[], isLastChunk);
  });

  const results = await Promise.allSettled(promises);

  results.forEach(result => {
    if (result.status === 'rejected') {
      console.error(
        `Publish event with type rendition_insert_row failed with ${result.reason} error`
      );
    }
  });
}
