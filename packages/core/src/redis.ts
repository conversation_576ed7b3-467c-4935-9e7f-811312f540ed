import { Cluster } from 'ioredis';
import isEmpty from 'lodash/isEmpty';
import { Logger } from '@aws-lambda-powertools/logger';
import { AppConfig } from '@ics/core/types';

import { RedlockMutex, LockOptions, LostLockError } from './redlock';

type Configs = {
  redisConfig?: AppConfig['Caching']['redis'] | {};
  redlockConfig: LockOptions;
  logger: Logger;
  options?: {
    appName: string;
  };
};

let redisInstance: Cluster | null = null;
let redlockInstance: RedlockMutex | null = null;

const MAX_RETRY_COUNT = 480;
const WAIT_TIME_IN_MS = 500;

const useRedis = ({
  redisConfig = {},
  redlockConfig = {} as RedlockMutex,
  options: { appName } = { appName: 'rendition' },
  logger,
}: Configs) => {
  if (isEmpty(redisConfig)) {
    logger.info(
      '[UseRedis] Redis config is empty. Reading initialized RedisInstance'
    );

    if (!redisInstance) {
      logger.info('[UseRedis] Redis Instance is not initialized');
    }
  }

  if (!redisInstance) {
    logger.info(`[UseRedis] Redis config`, redisConfig);
    const { host, port, ssl, username } =
      redisConfig as AppConfig['Caching']['redis'];
    const connection = {
      host,
      port,
    };

    const redisOptions = {
      username,
      connectTimeout: 30000,
      reconnectOnError: (error: Error) => {
        const targetErrors = [/READONLY/, /ETIMEDOUT/];
        return targetErrors.some(targetError =>
          targetError.test(error.message)
        );
      },
    };

    logger.info(`[UseRedis] Connecting to redis with user: ${username}`);

    if (ssl) {
      redisInstance = new Cluster([connection], {
        keyPrefix: `${appName}-`,
        clusterRetryStrategy: times => Math.min(times * 100, 2000),
        slotsRefreshTimeout: 15000,
        slotsRefreshInterval: 20000,
        dnsLookup: (address, callback) => callback(null, address),
        redisOptions: {
          ...redisOptions,
          tls: {},
        },
      });
    } else {
      redisInstance = new Cluster([connection], {
        keyPrefix: `${appName}-`,
        slotsRefreshTimeout: 15000,
        slotsRefreshInterval: 20000,
        clusterRetryStrategy: times => Math.min(times * 100, 2000),
        dnsLookup: (address, callback) => callback(null, address),
        redisOptions,
      });
    }

    redisInstance.on('error', error => {
      logger.info('[UseRedis] RedisInstance error:', error);
    });

    redisInstance.on('connect', () => {
      logger.info(`[UseRedis] Connected to Redis on ${host} : ${port}`);
    });

    redisInstance.on('reconnecting', () => {
      logger.info(`[UseRedis] Reconnecting to Redis on ${host} : ${port}`);
    });

    redisInstance.on('connecting', () => {
      logger.info(`[UseRedis] Connecting to Redis on ${host} : ${port}`);
    });

    redisInstance.on('close', () => {
      logger.info(`[UseRedis] Close connection to Redis on ${host} : ${port}`);
    });

    redisInstance.on('end', () => {
      logger.info('[UseRedis] Connection closed');
    });
  }

  if (isEmpty(redlockConfig)) {
    logger.info(
      '[UseRedis] Redlock config is empty. Reading initialized RedlockInstance'
    );

    if (!redlockInstance) {
      logger.info('[UseRedis] Redlock Instance is not initialized');
    }
  }

  if (!redlockInstance) {
    /**
     * lockTimeout - optional ms, time after mutex will be auto released (expired)
     * acquireTimeout - optional ms, max timeout for .acquire() call
     * acquireAttemptsLimit - optional max number of attempts to be made in .acquire() call
     * retryInterval - optional ms, time between acquire attempts if resource locked
     * refreshInterval - optional ms, auto-refresh interval; to disable auto-refresh behaviour set 0
     * identifier - optional uuid, custom mutex identifier. Must be unique between parallel executors, otherwise multiple locks with same identifier can be treated as the same lock holder. Override only if you know what you are doing (see acquiredExternally option).
     * acquiredExternally - optional true, If identifier provided and acquiredExternally is true then _refresh will be used instead of _acquire in .tryAcquire()/.acquire(). Useful for lock sharing between processes: acquire in scheduler, refresh and release in handler.
     * onLockLost - optional function, called when lock loss is detected due refresh cycle; default onLockLost throws unhandled LostLockError
     */

    const config: LockOptions = {
      lockTimeout: 3 * 60 * 1e3,
      acquireTimeout: 4 * 60 * 1e3,
      acquireAttemptsLimit: MAX_RETRY_COUNT,
      retryInterval: WAIT_TIME_IN_MS,
      refreshInterval: 0,
      onLockLost: (lostLockError: LostLockError) =>
        logger.error(`[RedlockMutex] Error: ${lostLockError?.message}, TenantId: ${redlockConfig?.identifier}`, { tenantId: redlockConfig?.identifier }),
      ...redlockConfig,
    };

    logger.info('[UseRedis] Creating a redlock instance');

    redlockInstance = new RedlockMutex(
      [redisInstance],
      redlockConfig?.identifier,
      config
    );
  }

  return { redis: redisInstance, redlock: redlockInstance };
};

export default useRedis;
