import { useMiddleware, MetricUnits } from '@ics/core/middlewares';
import { useDB } from '@ics/core/db';
import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';
import { sql } from 'kysely';

// Define the type for certificate reference records
type CertificateReferenceRecord = {
  id: number;
  target_id: string;
  certificate_expiry: string;
  count: number;
  last_notification_count: number | null;
  last_notification_date: Date | null;
  created_date: Date;
  updated_date: Date | null;
};

// Constants
const NOTIFICATION_THRESHOLD_INTERVAL = 100; // Send notification every 100 counts
const FINAL_THRESHOLD = 5000; // Final threshold value
const START_THRESHOLD = 4000; // Starting threshold value
const SES_REGION = 'ap-southeast-2'; // AWS SES region
const FROM_EMAIL = '<EMAIL>'; // Sender email address

// Initialize middleware
const { middyWrapper, logger, metrics } = useMiddleware('certificate-monitor');

// Initialize SES client
const sesClient = new SESClient({ region: SES_REGION });

/**
 * Retrieves email addresses for authorizers of a device
 * @param db - Database instance
 * @param deviceId - The device identifier
 * @returns Array of email addresses
 */
const getAuthorizerEmails = async (db: any, deviceId: string): Promise<string[]> => {
  try {
    // First, get the csr_request_session_id from csr_request_device table
    // Note: device_id in csr_request_device is a number, but our targetId might be a string
    const deviceIdNumber = parseInt(deviceId, 10);

    if (isNaN(deviceIdNumber)) {
      logger.warn(`Invalid device ID format: ${deviceId}`);
      return [];
    }

    const deviceRequests = await db
      .selectFrom('csr_request_device')
      .select(['csr_request_session_id'])
      .where('device_id', '=', deviceIdNumber)
      .orderBy('date_signing', 'desc')  // Get the most recent request
      .limit(1)
      .execute();

    if (!deviceRequests || deviceRequests.length === 0) {
      logger.warn(`No CSR request found for device ID: ${deviceId}`);
      return [];
    }

    const sessionId = deviceRequests[0].csr_request_session_id;

    // Get the authorizer user IDs from csr_request_session table
    const sessionInfo = await db
      .selectFrom('csr_request_session')
      .select(['authorizer_user_id', 'second_authorizer_user_id'])
      .where('csr_request_session_id', '=', sessionId)
      .executeTakeFirst();

    if (!sessionInfo) {
      logger.warn(`No session information found for session ID: ${sessionId}`);
      return [];
    }

    const userIds = [];
    if (sessionInfo.authorizer_user_id) userIds.push(sessionInfo.authorizer_user_id);
    if (sessionInfo.second_authorizer_user_id) userIds.push(sessionInfo.second_authorizer_user_id);

    if (userIds.length === 0) {
      logger.warn(`No authorizer user IDs found for session ID: ${sessionId}`);
      return [];
    }

    // Get the email addresses from the user table
    const users = await db
      .selectFrom('user')
      .select(['email'])
      .where('id', 'in', userIds)
      .execute();

    const emails = users.map((user: { email: string }) => user.email).filter(Boolean);

    logger.info(`Found ${emails.length} authorizer emails for device ID: ${deviceId}`, { emails });
    return emails;
  } catch (error) {
    logger.error(`Error retrieving authorizer emails for device ID: ${deviceId}`, { error });
    return [];
  }
};

/**
 * Sends an email notification about certificate count threshold
 * @param db - Database instance
 * @param targetId - The device identifier
 * @param count - The current count value
 * @param certificateExpiry - Certificate expiry information
 */
const sendThresholdNotification = async (
  db: any,
  targetId: string,
  count: number,
  certificateExpiry: string
) => {
  try {
    // Get authorizer emails dynamically from the database
    const toEmails = await getAuthorizerEmails(db, targetId);

    // If no authorizer emails found, log error and return
    if (toEmails.length === 0) {
      logger.error(`No email recipients found for device ID: ${targetId}`);
      return false;
    }

    const subject = `Certificate Count Threshold Alert for Device ${targetId}`;

    // Create HTML email content directly in the handler
    const body = `
      <html>
        <body>
          <h2>Certificate Count Threshold Alert</h2>
          <p>The certificate count for device <strong>${targetId}</strong> has reached <strong>${count}</strong>.</p>
          <p>Certificate expiry information: ${certificateExpiry}</p>
          <p>This is an automated notification as part of the certificate monitoring system.</p>

          <div>
            ${count >= FINAL_THRESHOLD
        ? `<p><strong>IMPORTANT: The count has reached the final threshold of ${FINAL_THRESHOLD}. Immediate action is required.</strong></p>
                 <p>This is the final notification you will receive for this device.</p>`
        : `<p>You will receive another notification when the count reaches ${count + NOTIFICATION_THRESHOLD_INTERVAL}.</p>
                 <p>Notifications will stop after reaching the final threshold of ${FINAL_THRESHOLD}.</p>`
      }
          </div>

          <hr>
          <p style="font-size: smaller;">This is an automated message. Please do not reply to this email.</p>
        </body>
      </html>
    `;

    const command = new SendEmailCommand({
      Destination: {
        ToAddresses: toEmails,
      },
      Message: {
        Body: {
          Html: {
            Charset: 'UTF-8',
            Data: body,
          },
        },
        Subject: {
          Charset: 'UTF-8',
          Data: subject,
        },
      },
      Source: FROM_EMAIL,
    });

    await sesClient.send(command);
    logger.info('Email notification sent successfully', { targetId, count, toEmails });

    // Record metric for successful email
    metrics.addMetric('EmailSent', MetricUnits.Count, 1);

    return true;
  } catch (error) {
    logger.error('Failed to send email notification', { error, targetId, count });

    // Record metric for failed email
    metrics.addMetric('EmailFailed', MetricUnits.Count, 1);

    return false;
  }
};

/**
 * Checks if a notification should be sent based on the count and last notification
 * @param count - Current count value
 * @param lastNotificationCount - Last count when notification was sent
 */
const shouldSendNotification = (count: number, lastNotificationCount: number | null): boolean => {
  // If count is below the starting threshold, don't send notification
  if (count < START_THRESHOLD) {
    return false;
  }

  // If count has reached or exceeded the final threshold, don't send any more notifications
  if (count >= FINAL_THRESHOLD) {
    // Only send one notification when exactly reaching the final threshold
    return lastNotificationCount !== null && lastNotificationCount < FINAL_THRESHOLD && count === FINAL_THRESHOLD;
  }

  // If no previous notification
  if (lastNotificationCount === null) {
    return true;
  }

  // Check if count has crossed another threshold interval
  const lastThreshold = Math.floor(lastNotificationCount / NOTIFICATION_THRESHOLD_INTERVAL) * NOTIFICATION_THRESHOLD_INTERVAL;
  const currentThreshold = Math.floor(count / NOTIFICATION_THRESHOLD_INTERVAL) * NOTIFICATION_THRESHOLD_INTERVAL;

  return currentThreshold > lastThreshold;
};

/**
 * Main handler function for the Lambda
 */
const handler = async (event: any) => {
  logger.info('Certificate monitoring Lambda started', { event });

  try {
    const appConfig = event.appConfig;
    const db = useDB(appConfig);

    // Record metric for function invocation
    metrics.addMetric('FunctionInvoked', MetricUnits.Count, 1);

    // Query certificate references that need monitoring
    const certificateReferences = await db
      .selectFrom('certificate_references')
      .selectAll()
      .execute();

    logger.info(`Found ${certificateReferences.length} certificate references to monitor`);

    // Process each certificate reference
    const updatePromises = certificateReferences.map(async (reference: CertificateReferenceRecord) => {
      const { target_id, certificate_expiry, count, last_notification_count } = reference;

      // Check if notification should be sent
      if (shouldSendNotification(count, last_notification_count)) {
        if (count === FINAL_THRESHOLD) {
          logger.info(`Sending FINAL notification for target_id: ${target_id} with count: ${count} (reached final threshold)`);
        } else {
          logger.info(`Sending notification for target_id: ${target_id} with count: ${count}`);
        }

        // Send email notification
        const emailSent = await sendThresholdNotification(
          db,
          target_id,
          count,
          certificate_expiry
        );

        // Update the last notification details if email was sent successfully
        if (emailSent) {
          await db
            .updateTable('certificate_references')
            .set({
              last_notification_count: count,
              last_notification_date: sql`CURRENT_TIMESTAMP`,
              updated_date: sql`CURRENT_TIMESTAMP`
            })
            .where('target_id', '=', target_id)
            .execute();

          logger.info(`Updated last notification details for target_id: ${target_id}`);
        }
      } else {
        if (count >= FINAL_THRESHOLD) {
          logger.info(`No notification sent for target_id: ${target_id} with count: ${count} (already exceeded final threshold)`);
        } else if (count < START_THRESHOLD) {
          logger.info(`No notification needed for target_id: ${target_id} with count: ${count} (below starting threshold)`);
        } else {
          logger.info(`No notification needed for target_id: ${target_id} with count: ${count} (hasn't reached next threshold interval)`);
        }
      }
    });

    // Wait for all updates to complete
    await Promise.all(updatePromises);

    logger.info('Certificate monitoring completed successfully');
    return { statusCode: 200, body: 'Certificate monitoring completed successfully' };
  } catch (error) {
    logger.error('Error in certificate monitoring', { error });

    // Record metric for function error
    metrics.addMetric('FunctionError', MetricUnits.Count, 1);

    return { statusCode: 500, body: 'Error in certificate monitoring' };
  }
};

export const main = middyWrapper(handler);
